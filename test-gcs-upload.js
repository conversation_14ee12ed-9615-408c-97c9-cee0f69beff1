const axios = require('axios');
const fs = require('fs');
const path = require('path');
const FormData = require('form-data');

async function testGCSUpload() {
  console.log('🧪 Testing GCS Upload Process');
  console.log('==============================');

  // Check if we have test files
  const testResultsDir = path.join(process.cwd(), 'test-results');
  const videoFiles = [];
  const screenshotFiles = [];

  function findFiles(dir, extension, fileArray) {
    try {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory()) {
          findFiles(fullPath, extension, fileArray);
        } else if (item.endsWith(extension)) {
          fileArray.push({ path: fullPath, mtime: stat.mtime, size: stat.size });
        }
      }
    } catch (error) {
      // Ignore errors
    }
  }

  if (fs.existsSync(testResultsDir)) {
    findFiles(testResultsDir, '.webm', videoFiles);
    findFiles(testResultsDir, '.png', screenshotFiles);
  }

  console.log(`📹 Found ${videoFiles.length} video files`);
  console.log(`📸 Found ${screenshotFiles.length} screenshot files`);

  if (videoFiles.length === 0 && screenshotFiles.length === 0) {
    console.log('❌ No test files found. Run a test first to generate video/screenshot files.');
    return;
  }

  // Test backend connectivity
  const backendUrl = 'http://localhost:3010';
  
  try {
    console.log('\n🔗 Testing backend connectivity...');
    const healthResponse = await axios.get(`${backendUrl}/health`, { timeout: 5000 });
    console.log('✅ Backend is accessible');
  } catch (error) {
    console.log('❌ Backend not accessible:', error.message);
    return;
  }

  // Try to get projects (this will fail without auth but tells us the endpoint exists)
  try {
    console.log('\n📋 Testing projects endpoint...');
    const projectsResponse = await axios.get(`${backendUrl}/projects`, { timeout: 5000 });
    console.log('✅ Projects endpoint accessible');
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Projects endpoint exists (authentication required)');
    } else {
      console.log('⚠️  Projects endpoint response:', error.response?.status || 'No response');
    }
  }

  // Test a specific upload endpoint (this will fail but we can see the error)
  if (videoFiles.length > 0) {
    console.log('\n🎥 Testing video upload endpoint...');
    const testVideo = videoFiles.sort((a, b) => b.mtime - a.mtime)[0];
    console.log(`Using test video: ${testVideo.path} (${(testVideo.size / 1024 / 1024).toFixed(2)} MB)`);

    try {
      const videoBuffer = fs.readFileSync(testVideo.path);
      const formData = new FormData();
      formData.append('file', videoBuffer, {
        filename: 'video.webm',
        contentType: 'video/webm'
      });

      // Try to upload to a test endpoint
      const uploadResponse = await axios.post(
        `${backendUrl}/projects/test-project/test-runs/test-run/test-results/test-result/video`,
        formData,
        {
          headers: {
            ...formData.getHeaders(),
            'Authorization': 'Bearer test-token'
          },
          timeout: 10000
        }
      );
      console.log('✅ Video upload successful');
    } catch (error) {
      if (error.response) {
        console.log(`📊 Video upload response: ${error.response.status} - ${error.response.statusText}`);
        console.log(`📊 Response data:`, error.response.data);
        
        if (error.response.status === 404) {
          console.log('❌ Video upload endpoint does not exist');
        } else if (error.response.status === 401) {
          console.log('✅ Video upload endpoint exists (authentication required)');
        } else if (error.response.status === 500) {
          console.log('⚠️  Server error - possibly GCS configuration issue');
        }
      } else {
        console.log('❌ Video upload failed:', error.message);
      }
    }
  }

  // Check environment variables
  console.log('\n🔧 Checking GCS configuration...');
  const envFile = path.join(process.cwd(), '.env');
  if (fs.existsSync(envFile)) {
    const envContent = fs.readFileSync(envFile, 'utf8');
    
    const gcpProjectId = envContent.match(/GCP_PROJECT_ID=(.+)/)?.[1]?.trim();
    const gcpClientEmail = envContent.match(/GCP_CLIENT_EMAIL=(.+)/)?.[1]?.trim();
    const gcpBucketName = envContent.match(/GCP_BUCKET_NAME=(.+)/)?.[1]?.trim();
    
    console.log(`GCP_PROJECT_ID: ${gcpProjectId}`);
    console.log(`GCP_CLIENT_EMAIL: ${gcpClientEmail}`);
    console.log(`GCP_BUCKET_NAME: ${gcpBucketName}`);
    
    if (gcpProjectId?.includes('your_') || gcpClientEmail?.includes('your_')) {
      console.log('❌ GCS credentials are placeholder values');
      console.log('💡 Update .env file with real GCS credentials to enable uploads');
    } else {
      console.log('✅ GCS credentials appear to be configured');
    }
  }

  console.log('\n📋 Summary:');
  console.log('===========');
  console.log('1. Test files are being generated ✅');
  console.log('2. Backend is accessible ✅');
  console.log('3. Upload endpoints may exist ⚠️');
  console.log('4. GCS credentials need to be configured ❌');
  console.log('\n💡 Next steps:');
  console.log('- Configure real GCS credentials in .env file');
  console.log('- Restart backend services');
  console.log('- Run a test and check backend logs for upload success/failure');
}

testGCSUpload().catch(console.error);
