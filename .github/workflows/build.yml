name: Build and Push to GCR

on:
  push:
    branches:
      - staging
      - production

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v2

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v1

    - name: Log in to Google Cloud
      uses: google-github-actions/auth@v0
      with:
        credentials_json: \${{ secrets.GCP_CREDENTIALS }}

    - name: Configure Docker to use the gcloud command-line tool as a credential helper
      run: |
        gcloud auth configure-docker

    - name: Build Docker image for staging
      if: github.ref == 'refs/heads/staging'
      run: |
        source .env.staging && docker build --build-arg VITE_CORE_SERVICE_URL=\${VITE_CORE_SERVICE_URL} --build-arg VITE_BACKEND_URL=\${VITE_BACKEND_URL} --build-arg VITE_AI_SERVICE_URL=\${VITE_AI_SERVICE_URL} --build-arg VITE_WEBSOCKET_URL=\${VITE_WEBSOCKET_URL} --build-arg VITE_WEBSOCKET_TESTRUN_URL=\${VITE_WEBSOCKET_TESTRUN_URL} -t gcr.io/\${{ secrets.GCP_PROJECT_ID }}/app_frontend_agentq-staging:\${{ github.sha }} .

    - name: Push Docker image to staging
      if: github.ref == 'refs/heads/staging'
      run: |
        docker push gcr.io/\${{ secrets.GCP_PROJECT_ID }}/app_frontend_agentq-staging:\${{ github.sha }}

    - name: Build Docker image for production
      if: github.ref == 'refs/heads/production'
      run: |
        source .env.production && docker build --build-arg VITE_CORE_SERVICE_URL=\${VITE_CORE_SERVICE_URL} --build-arg VITE_BACKEND_URL=\${VITE_BACKEND_URL} --build-arg VITE_AI_SERVICE_URL=\${VITE_AI_SERVICE_URL} --build-arg VITE_WEBSOCKET_URL=\${VITE_WEBSOCKET_URL} --build-arg VITE_WEBSOCKET_TESTRUN_URL=\${VITE_WEBSOCKET_TESTRUN_URL} -t gcr.io/\${{ secrets.GCP_PROJECT_ID }}/app_frontend_agentq:\${{ github.sha }} .

    - name: Push Docker image to production
      if: github.ref == 'refs/heads/production'
      run: |
        docker push gcr.io/\${{ secrets.GCP_PROJECT_ID }}/app_frontend_agentq:\${{ github.sha }}
