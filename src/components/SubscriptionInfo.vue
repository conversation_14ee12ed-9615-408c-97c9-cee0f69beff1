<template>
  <div class="subscription-info">
    <div v-if="loading">Loading subscription information...</div>
    <div v-else-if="error" class="error">{{ error }}</div>
    <div v-else class="subscription-details">
      <h3>Subscription: {{ profile?.company?.subscription?.name }}</h3>
      <div class="token-info">
        <p>Token Limit: {{ profile?.company?.subscription?.tokenLimit?.toLocaleString() }}</p>
        <p>Tokens Remaining: {{ profile?.company?.subscription?.remainingTokens?.toLocaleString() }}</p>
        <p v-if="profile?.company?.subscription?.isEnterprise">
          Enterprise subscription valid until: 
          {{ formatDate(profile?.company?.subscription?.endDate) }}
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { profileService } from '../services/profileService';
import { Profile } from '../types/index';


const profile = ref<Profile | null>(null);
const loading = ref(true);
const error = ref('');

const formatDate = (dateString: string) => {
  if (!dateString) return 'N/A';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

onMounted(async () => {
  try {
    loading.value = true;
    profile.value = await profileService.getProfile();
  } catch (err) {
    error.value = 'Failed to load subscription information';
    console.error(err);
  } finally {
    loading.value = false;
  }
});
</script>