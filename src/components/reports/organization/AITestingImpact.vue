<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { <PERSON>hnut, Bar } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
} from 'chart.js';
import { useReportData } from '../../../composables/useReportData';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement
);

const { getAITestingImpactWithFallback, loading, error } = useReportData();

const aiData = ref<any>(null);

// Chart options
const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
};

const barOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

// Chart data
const aiTestCasesData = ref({
  labels: ['Manual/Traditional', 'AI-Generated'],
  datasets: [
    {
      data: [0, 0],
      backgroundColor: ['#6b7280', '#8b5cf6'],
      borderWidth: 0,
    },
  ],
});

const aiVsManualResultsData = ref({
  labels: ['AI-Generated Tests', 'Manual/Traditional Tests'],
  datasets: [
    {
      label: 'Passed',
      data: [0, 0],
      backgroundColor: '#10b981',
    },
    {
      label: 'Failed',
      data: [0, 0],
      backgroundColor: '#ef4444',
    },
  ],
});

const fetchData = async () => {
  try {
    const ai = await getAITestingImpactWithFallback();
    aiData.value = ai;

    // Update AI test cases distribution
    if (ai.aiTestCases) {
      const aiGenerated = ai.aiTestCases.find((item: any) => item.isAIGenerated)?.count || 0;
      const traditional = ai.aiTestCases.find((item: any) => !item.isAIGenerated)?.count || 0;
      
      aiTestCasesData.value = {
        labels: ['Manual/Traditional', 'AI-Generated'],
        datasets: [
          {
            data: [parseInt(traditional), parseInt(aiGenerated)],
            backgroundColor: ['#6b7280', '#8b5cf6'],
            borderWidth: 0,
          },
        ],
      };
    }

    // Update AI vs manual results comparison
    if (ai.aiVsManualResults) {
      const aiPassed = ai.aiVsManualResults.find((item: any) => item.isAIGenerated && item.status === 'passed')?.count || 0;
      const aiFailed = ai.aiVsManualResults.find((item: any) => item.isAIGenerated && item.status === 'failed')?.count || 0;
      const manualPassed = ai.aiVsManualResults.find((item: any) => !item.isAIGenerated && item.status === 'passed')?.count || 0;
      const manualFailed = ai.aiVsManualResults.find((item: any) => !item.isAIGenerated && item.status === 'failed')?.count || 0;
      
      aiVsManualResultsData.value = {
        labels: ['AI-Generated Tests', 'Manual/Traditional Tests'],
        datasets: [
          {
            label: 'Passed',
            data: [parseInt(aiPassed), parseInt(manualPassed)],
            backgroundColor: '#10b981',
          },
          {
            label: 'Failed',
            data: [parseInt(aiFailed), parseInt(manualFailed)],
            backgroundColor: '#ef4444',
          },
        ],
      };
    }
  } catch (err) {
    console.error('Failed to fetch AI testing impact data:', err);
  }
};

// Calculate AI impact metrics
const calculateAIMetrics = () => {
  if (!aiData.value) return { adoption: 0, successRate: 0, timeSaved: 0, efficiency: 0 };

  const aiGenerated = aiData.value.aiTestCases?.find((item: any) => item.isAIGenerated)?.count || 0;
  const traditional = aiData.value.aiTestCases?.find((item: any) => !item.isAIGenerated)?.count || 0;
  const total = parseInt(aiGenerated) + parseInt(traditional);
  const adoption = total > 0 ? Math.round((parseInt(aiGenerated) / total) * 100) : 0;

  const aiPassed = aiData.value.aiVsManualResults?.find((item: any) => item.isAIGenerated && item.status === 'passed')?.count || 0;
  const aiTotal = aiData.value.aiVsManualResults?.filter((item: any) => item.isAIGenerated)
    .reduce((sum: number, item: any) => sum + parseInt(item.count), 0) || 0;
  const successRate = aiTotal > 0 ? Math.round((parseInt(aiPassed) / aiTotal) * 100) : 0;

  // Estimate time saved (assuming AI tests are created 5x faster)
  const timeSaved = parseInt(aiGenerated) * 4; // 4 hours saved per AI test (5x faster = 80% time saved)
  
  // Calculate efficiency gain
  const efficiency = adoption > 0 ? Math.round((timeSaved / (parseInt(aiGenerated) * 5)) * 100) : 0;

  return { adoption, successRate, timeSaved, efficiency };
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="ai-testing-impact">
    <div class="impact-header">
      <h2>AI Testing Impact</h2>
      <p>Analyze the effectiveness and impact of AI-generated test cases</p>
    </div>

    <div v-if="loading" class="loading">
      Loading AI testing impact data...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else class="impact-content">
      <div class="impact-grid">
        <!-- AI Impact Summary -->
        <div class="summary-card full-width">
          <h3>AI Testing Impact Summary</h3>
          <div class="summary-stats">
            <div class="summary-item">
              <div class="summary-value">{{ calculateAIMetrics().adoption }}%</div>
              <div class="summary-label">AI Adoption Rate</div>
            </div>
            <div class="summary-item">
              <div class="summary-value">{{ calculateAIMetrics().successRate }}%</div>
              <div class="summary-label">AI Test Success Rate</div>
            </div>
            <div class="summary-item">
              <div class="summary-value">{{ calculateAIMetrics().timeSaved }}</div>
              <div class="summary-label">Hours Saved</div>
            </div>
            <div class="summary-item">
              <div class="summary-value">{{ calculateAIMetrics().efficiency }}%</div>
              <div class="summary-label">Efficiency Gain</div>
            </div>
          </div>
        </div>

        <!-- AI Test Cases Distribution -->
        <div class="chart-card">
          <h3>Test Case Generation</h3>
          <div class="chart-container">
            <Doughnut :data="aiTestCasesData" :options="doughnutOptions" />
          </div>
          <div class="chart-summary">
            <div class="summary-item">
              <span class="summary-label">AI-Generated Tests:</span>
              <span class="summary-value">
                {{ aiData?.aiTestCases?.find((item: any) => item.isAIGenerated)?.count || 0 }}
              </span>
            </div>
          </div>
        </div>

        <!-- AI vs Manual Results -->
        <div class="chart-card">
          <h3>AI vs Manual Test Results</h3>
          <div class="chart-container">
            <Bar :data="aiVsManualResultsData" :options="barOptions" />
          </div>
        </div>

        <!-- AI Effectiveness Metrics -->
        <div class="metrics-card">
          <h3>AI Effectiveness Metrics</h3>
          <div class="metrics-list">
            <div class="metric-item">
              <div class="metric-label">AI Test Cases Created</div>
              <div class="metric-value">
                {{ aiData?.aiTestCases?.find((item: any) => item.isAIGenerated)?.count || 0 }}
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">AI Test Success Rate</div>
              <div class="metric-value">{{ calculateAIMetrics().successRate }}%</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Traditional Test Success Rate</div>
              <div class="metric-value">
                {{ 
                  aiData?.aiVsManualResults ? 
                  Math.round((parseInt(aiData.aiVsManualResults.find((item: any) => !item.isAIGenerated && item.status === 'passed')?.count || '0') / 
                  aiData.aiVsManualResults.filter((item: any) => !item.isAIGenerated).reduce((sum: number, item: any) => sum + parseInt(item.count), 0)) * 100) : 0 
                }}%
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Time Saved (Estimated)</div>
              <div class="metric-value">{{ calculateAIMetrics().timeSaved }} hours</div>
            </div>
          </div>
        </div>

        <!-- AI Recommendations -->
        <div class="recommendations-card">
          <h3>AI Testing Recommendations</h3>
          <div class="recommendations-list">
            <div 
              v-if="calculateAIMetrics().adoption < 20" 
              class="recommendation-item"
            >
              <span class="recommendation-icon">🤖</span>
              <span>AI adoption is low. Consider increasing AI-generated test case usage to improve efficiency.</span>
            </div>
            <div 
              v-if="calculateAIMetrics().successRate > 85" 
              class="recommendation-item success"
            >
              <span class="recommendation-icon">✅</span>
              <span>AI-generated tests have excellent success rate! Continue leveraging AI for test creation.</span>
            </div>
            <div 
              v-if="calculateAIMetrics().successRate < 70" 
              class="recommendation-item"
            >
              <span class="recommendation-icon">⚠️</span>
              <span>AI test success rate is below 70%. Review AI-generated test quality and improve prompts.</span>
            </div>
            <div 
              v-if="calculateAIMetrics().timeSaved > 100" 
              class="recommendation-item success"
            >
              <span class="recommendation-icon">⚡</span>
              <span>Significant time savings achieved through AI! Consider expanding AI usage to more projects.</span>
            </div>
            <div 
              v-if="calculateAIMetrics().adoption >= 20 && calculateAIMetrics().successRate >= 80" 
              class="recommendation-item success"
            >
              <span class="recommendation-icon">🎯</span>
              <span>Excellent AI testing adoption and effectiveness! You're maximizing AI benefits.</span>
            </div>
          </div>
        </div>

        <!-- AI vs Manual Comparison Table -->
        <div class="comparison-card full-width">
          <h3>AI vs Manual Testing Comparison</h3>
          <div class="comparison-table">
            <table>
              <thead>
                <tr>
                  <th>Metric</th>
                  <th>AI-Generated</th>
                  <th>Manual/Traditional</th>
                  <th>Difference</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Total Test Cases</td>
                  <td>{{ aiData?.aiTestCases?.find((item: any) => item.isAIGenerated)?.count || 0 }}</td>
                  <td>{{ aiData?.aiTestCases?.find((item: any) => !item.isAIGenerated)?.count || 0 }}</td>
                  <td>
                    {{ 
                      (parseInt(aiData?.aiTestCases?.find((item: any) => item.isAIGenerated)?.count || '0') - 
                      parseInt(aiData?.aiTestCases?.find((item: any) => !item.isAIGenerated)?.count || '0')) > 0 ? '+' : ''
                    }}{{ 
                      parseInt(aiData?.aiTestCases?.find((item: any) => item.isAIGenerated)?.count || '0') - 
                      parseInt(aiData?.aiTestCases?.find((item: any) => !item.isAIGenerated)?.count || '0') 
                    }}
                  </td>
                </tr>
                <tr>
                  <td>Success Rate</td>
                  <td>{{ calculateAIMetrics().successRate }}%</td>
                  <td>
                    {{ 
                      aiData?.aiVsManualResults ? 
                      Math.round((parseInt(aiData.aiVsManualResults.find((item: any) => !item.isAIGenerated && item.status === 'passed')?.count || '0') / 
                      aiData.aiVsManualResults.filter((item: any) => !item.isAIGenerated).reduce((sum: number, item: any) => sum + parseInt(item.count), 0)) * 100) : 0 
                    }}%
                  </td>
                  <td>
                    {{ 
                      calculateAIMetrics().successRate - (aiData?.aiVsManualResults ? 
                      Math.round((parseInt(aiData.aiVsManualResults.find((item: any) => !item.isAIGenerated && item.status === 'passed')?.count || '0') / 
                      aiData.aiVsManualResults.filter((item: any) => !item.isAIGenerated).reduce((sum: number, item: any) => sum + parseInt(item.count), 0)) * 100) : 0) > 0 ? '+' : ''
                    }}{{ 
                      calculateAIMetrics().successRate - (aiData?.aiVsManualResults ? 
                      Math.round((parseInt(aiData.aiVsManualResults.find((item: any) => !item.isAIGenerated && item.status === 'passed')?.count || '0') / 
                      aiData.aiVsManualResults.filter((item: any) => !item.isAIGenerated).reduce((sum: number, item: any) => sum + parseInt(item.count), 0)) * 100) : 0) 
                    }}%
                  </td>
                </tr>
                <tr>
                  <td>Estimated Creation Time</td>
                  <td>{{ parseInt(aiData?.aiTestCases?.find((item: any) => item.isAIGenerated)?.count || '0') * 1 }} hours</td>
                  <td>{{ parseInt(aiData?.aiTestCases?.find((item: any) => !item.isAIGenerated)?.count || '0') * 5 }} hours</td>
                  <td class="time-saved">
                    -{{ calculateAIMetrics().timeSaved }} hours saved
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.ai-testing-impact {
  .impact-header {
    margin-bottom: 32px;

    h2 {
      font-size: 24px;
      font-weight: 700;
      color: #111827;
      margin: 0 0 8px 0;
    }

    p {
      color: #6b7280;
      margin: 0;
    }
  }

  .loading, .error {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .error {
    color: #ef4444;
  }
}

.impact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;

  .full-width {
    grid-column: 1 / -1;
  }
}

.chart-card, .summary-card, .metrics-card, .recommendations-card, .comparison-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }
}

.chart-container {
  height: 300px;
  position: relative;
}

.chart-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.summary-item {
  text-align: center;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;

  &:not(.summary-stats .summary-item) {
    display: flex;
    justify-content: space-between;
    align-items: center;
    text-align: left;
    background: transparent;
    padding: 0;
  }

  .summary-value {
    font-size: 24px;
    font-weight: 700;
    color: #0369a1;
    margin-bottom: 4px;

    .summary-stats & {
      font-size: 24px;
      margin-bottom: 4px;
    }

    &:not(.summary-stats .summary-value) {
      font-size: 16px;
      font-weight: 600;
      color: #111827;
      margin-bottom: 0;
    }
  }

  .summary-label {
    font-size: 12px;
    color: #0369a1;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    .summary-stats & {
      font-size: 12px;
      color: #0369a1;
    }

    &:not(.summary-stats .summary-label) {
      font-size: 14px;
      color: #6b7280;
      text-transform: none;
      letter-spacing: normal;
    }
  }
}

.metrics-list {
  display: grid;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;

  .metric-label {
    font-size: 14px;
    color: #6b7280;
  }

  .metric-value {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  }
}

.recommendations-list {
  display: grid;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fef3c7;
  border-radius: 6px;
  font-size: 14px;
  color: #92400e;

  &.success {
    background: #d1fae5;
    color: #065f46;
  }

  .recommendation-icon {
    font-size: 16px;
  }
}

.comparison-table {
  overflow-x: auto;

  table {
    width: 100%;
    border-collapse: collapse;

    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e5e7eb;
    }

    th {
      background: #f9fafb;
      font-weight: 600;
      color: #374151;
    }

    .time-saved {
      color: #10b981;
      font-weight: 600;
    }
  }
}

@media (max-width: 768px) {
  .impact-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 250px;
  }

  .summary-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
