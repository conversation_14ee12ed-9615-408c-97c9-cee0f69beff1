<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Bar } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
} from 'chart.js';
import { useReportData } from '../../../composables/useReportData';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement
);

const { getCrossProjectQualityComparisonWithFallback, loading, error } = useReportData();

const qualityData = ref<any>(null);

// Chart options
const barOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

// Chart data
const testCoverageData = ref({
  labels: [],
  datasets: [
    {
      label: 'Total Test Cases',
      data: [],
      backgroundColor: '#3b82f6',
    },
    {
      label: 'Automated Test Cases',
      data: [],
      backgroundColor: '#10b981',
    },
  ],
});

const passRatesData = ref({
  labels: [],
  datasets: [
    {
      label: 'Pass Rate %',
      data: [],
      backgroundColor: '#10b981',
    },
  ],
});

const fetchData = async () => {
  try {
    const quality = await getCrossProjectQualityComparisonWithFallback();
    qualityData.value = quality;

    // Update test coverage chart
    if (quality.projectCoverage) {
      testCoverageData.value = {
        labels: quality.projectCoverage.map((item: any) => item.projectName),
        datasets: [
          {
            label: 'Total Test Cases',
            data: quality.projectCoverage.map((item: any) => parseInt(item.totalTestCases)),
            backgroundColor: '#3b82f6',
          },
          {
            label: 'Automated Test Cases',
            data: quality.projectCoverage.map((item: any) => parseInt(item.automatedTestCases)),
            backgroundColor: '#10b981',
          },
        ],
      };
    }

    // Update pass rates chart
    if (quality.projectPassRates) {
      passRatesData.value = {
        labels: quality.projectPassRates.map((item: any) => item.projectName),
        datasets: [
          {
            label: 'Pass Rate %',
            data: quality.projectPassRates.map((item: any) => 
              Math.round((parseInt(item.passedTests) / parseInt(item.totalTests)) * 100)
            ),
            backgroundColor: quality.projectPassRates.map((item: any) => {
              const passRate = (parseInt(item.passedTests) / parseInt(item.totalTests)) * 100;
              if (passRate >= 90) return '#10b981';
              if (passRate >= 75) return '#f59e0b';
              return '#ef4444';
            }),
          },
        ],
      };
    }
  } catch (err) {
    console.error('Failed to fetch project quality comparison data:', err);
  }
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="project-quality-comparison">
    <div class="comparison-header">
      <h2>Project Quality Comparison</h2>
      <p>Compare test coverage and quality metrics across all projects</p>
    </div>

    <div v-if="loading" class="loading">
      Loading project quality comparison...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else class="comparison-content">
      <div class="comparison-grid">
        <!-- Test Coverage Comparison -->
        <div class="chart-card full-width">
          <h3>Test Coverage by Project</h3>
          <div class="chart-container">
            <Bar :data="testCoverageData" :options="barOptions" />
          </div>
        </div>

        <!-- Pass Rates Comparison -->
        <div class="chart-card full-width">
          <h3>Pass Rates Across Projects</h3>
          <div class="chart-container">
            <Bar :data="passRatesData" :options="barOptions" />
          </div>
        </div>

        <!-- Project Details Table -->
        <div class="table-card full-width">
          <h3>Project Details</h3>
          <div class="project-table">
            <table>
              <thead>
                <tr>
                  <th>Project</th>
                  <th>Total Tests</th>
                  <th>Automated</th>
                  <th>Automation %</th>
                  <th>Pass Rate</th>
                  <th>Status</th>
                </tr>
              </thead>
              <tbody>
                <tr 
                  v-for="project in qualityData?.projectCoverage || []" 
                  :key="project.projectName"
                >
                  <td class="project-name">{{ project.projectName }}</td>
                  <td>{{ project.totalTestCases }}</td>
                  <td>{{ project.automatedTestCases }}</td>
                  <td>
                    {{ Math.round((parseInt(project.automatedTestCases) / parseInt(project.totalTestCases)) * 100) }}%
                  </td>
                  <td>
                    {{ 
                      qualityData?.projectPassRates?.find((p: any) => p.projectName === project.projectName) ? 
                      Math.round((parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).passedTests) / 
                      parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).totalTests)) * 100) : 'N/A' 
                    }}%
                  </td>
                  <td>
                    <span 
                      class="status-badge"
                      :class="{
                        'healthy': qualityData?.projectPassRates?.find((p: any) => p.projectName === project.projectName) && 
                          (parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).passedTests) / 
                          parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).totalTests)) >= 0.9,
                        'warning': qualityData?.projectPassRates?.find((p: any) => p.projectName === project.projectName) && 
                          (parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).passedTests) / 
                          parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).totalTests)) >= 0.75 &&
                          (parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).passedTests) / 
                          parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).totalTests)) < 0.9,
                        'critical': qualityData?.projectPassRates?.find((p: any) => p.projectName === project.projectName) && 
                          (parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).passedTests) / 
                          parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).totalTests)) < 0.75
                      }"
                    >
                      {{
                        qualityData?.projectPassRates?.find((p: any) => p.projectName === project.projectName) ? 
                        ((parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).passedTests) / 
                        parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).totalTests)) >= 0.9 ? 'Healthy' :
                        (parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).passedTests) / 
                        parseInt(qualityData.projectPassRates.find((p: any) => p.projectName === project.projectName).totalTests)) >= 0.75 ? 'Warning' : 'Critical') : 'Unknown'
                      }}
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.project-quality-comparison {
  .comparison-header {
    margin-bottom: 32px;

    h2 {
      font-size: 24px;
      font-weight: 700;
      color: #111827;
      margin: 0 0 8px 0;
    }

    p {
      color: #6b7280;
      margin: 0;
    }
  }

  .loading, .error {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .error {
    color: #ef4444;
  }
}

.comparison-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;

  .full-width {
    grid-column: 1 / -1;
  }
}

.chart-card, .table-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }
}

.chart-container {
  height: 400px;
  position: relative;
}

.project-table {
  overflow-x: auto;

  table {
    width: 100%;
    border-collapse: collapse;

    th, td {
      padding: 12px;
      text-align: left;
      border-bottom: 1px solid #e5e7eb;
    }

    th {
      background: #f9fafb;
      font-weight: 600;
      color: #374151;
    }

    .project-name {
      font-weight: 500;
      color: #111827;
    }
  }
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;

  &.healthy {
    background: #d1fae5;
    color: #065f46;
  }

  &.warning {
    background: #fef3c7;
    color: #92400e;
  }

  &.critical {
    background: #fee2e2;
    color: #991b1b;
  }
}

@media (max-width: 768px) {
  .chart-container {
    height: 300px;
  }
}
</style>
