<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Line, Bar } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { useReportData } from '../../../composables/useReportData';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement
);

const { getOrganizationTestingHealthWithFallback, loading, error } = useReportData();

const healthData = ref<any>(null);

// Chart options
const lineOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

const barOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

// Chart data
const qualityTrendsData = ref({
  labels: [],
  datasets: [
    {
      label: 'Pass Rate %',
      data: [],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4,
    },
  ],
});

const testingVelocityData = ref<{
  labels: string[];
  datasets: any[];
}>({
  labels: [],
  datasets: [],
});

const fetchData = async () => {
  try {
    const health = await getOrganizationTestingHealthWithFallback();
    healthData.value = health;

    // Update quality trends
    if (health.qualityTrends) {
      qualityTrendsData.value = {
        labels: health.qualityTrends.map((item: any) => 
          new Date(item.week).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
        ),
        datasets: [
          {
            label: 'Pass Rate %',
            data: health.qualityTrends.map((item: any) => 
              Math.round((parseInt(item.passedTests) / parseInt(item.totalTests)) * 100)
            ),
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4,
          },
        ],
      };
    }

    // Update testing velocity
    if (health.testingVelocity) {
      const projects = [...new Set(health.testingVelocity.map((item: any) => item.projectName))];
      const months = [...new Set(health.testingVelocity.map((item: any) => item.month))].sort();
      
      const colors = ['#3b82f6', '#8b5cf6', '#f59e0b', '#ef4444', '#10b981'];
      
      testingVelocityData.value = {
        labels: months.map(month => 
          new Date(month as string).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
        ),
        datasets: projects.map((project, index) => ({
          label: project,
          data: months.map(month => {
            const item = health.testingVelocity.find((v: any) => 
              v.projectName === project && v.month === month
            );
            return item ? parseInt(item.testRunCount) : 0;
          }),
          backgroundColor: colors[index % colors.length],
        })),
      };
    }
  } catch (err) {
    console.error('Failed to fetch organization testing health data:', err);
  }
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="organization-testing-health">
    <div class="health-header">
      <h2>Organization Testing Health</h2>
      <p>Monitor testing velocity and quality trends across the organization</p>
    </div>

    <div v-if="loading" class="loading">
      Loading organization testing health...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else class="health-content">
      <div class="health-grid">
        <!-- Quality Trends -->
        <div class="chart-card full-width">
          <h3>Organization Quality Trends</h3>
          <div class="chart-container">
            <Line :data="qualityTrendsData" :options="lineOptions" />
          </div>
        </div>

        <!-- Testing Velocity -->
        <div class="chart-card full-width">
          <h3>Testing Velocity by Project</h3>
          <div class="chart-container">
            <Bar :data="testingVelocityData" :options="barOptions" />
          </div>
        </div>

        <!-- Health Metrics -->
        <div class="metrics-card">
          <h3>Health Metrics</h3>
          <div class="metrics-list">
            <div class="metric-item">
              <div class="metric-label">Overall Automation Rate</div>
              <div class="metric-value">
                {{ 
                  healthData?.overallAutomation ? 
                  Math.round((parseInt(healthData.overallAutomation.find((item: any) => item.type === 'automation')?.count || '0') / 
                  healthData.overallAutomation.reduce((sum: number, item: any) => sum + parseInt(item.count), 0)) * 100) : 0 
                }}%
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Average Pass Rate</div>
              <div class="metric-value">
                {{ 
                  healthData?.qualityTrends?.length > 0 ? 
                  Math.round(healthData.qualityTrends.reduce((sum: number, item: any) => 
                    sum + (parseInt(item.passedTests) / parseInt(item.totalTests)) * 100, 0) / healthData.qualityTrends.length) : 0 
                }}%
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Active Projects</div>
              <div class="metric-value">
                {{ healthData?.testingVelocity ? [...new Set(healthData.testingVelocity.map((item: any) => item.projectName))].length : 0 }}
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Total Test Runs (Last 3 Months)</div>
              <div class="metric-value">
                {{ 
                  healthData?.testingVelocity ? 
                  healthData.testingVelocity.reduce((sum: number, item: any) => sum + parseInt(item.testRunCount), 0) : 0 
                }}
              </div>
            </div>
          </div>
        </div>

        <!-- Health Recommendations -->
        <div class="recommendations-card">
          <h3>Health Recommendations</h3>
          <div class="recommendations-list">
            <div 
              v-if="healthData?.overallAutomation && 
                Math.round((parseInt(healthData.overallAutomation.find((item: any) => item.type === 'automation')?.count || '0') / 
                healthData.overallAutomation.reduce((sum: number, item: any) => sum + parseInt(item.count), 0)) * 100) < 50" 
              class="recommendation-item"
            >
              <span class="recommendation-icon">⚡</span>
              <span>Organization automation rate is below 50%. Consider increasing automation efforts.</span>
            </div>
            <div 
              v-if="healthData?.qualityTrends?.length > 0 && 
                healthData.qualityTrends[healthData.qualityTrends.length - 1] && 
                (parseInt(healthData.qualityTrends[healthData.qualityTrends.length - 1].passedTests) / 
                parseInt(healthData.qualityTrends[healthData.qualityTrends.length - 1].totalTests)) < 0.8" 
              class="recommendation-item"
            >
              <span class="recommendation-icon">⚠️</span>
              <span>Recent pass rate is below 80%. Review failing tests and improve quality processes.</span>
            </div>
            <div 
              v-if="healthData?.testingVelocity && 
                healthData.testingVelocity.some((item: any) => parseInt(item.testRunCount) === 0)" 
              class="recommendation-item"
            >
              <span class="recommendation-icon">🔄</span>
              <span>Some projects have low testing velocity. Encourage more frequent test execution.</span>
            </div>
            <div 
              v-if="healthData?.overallAutomation && 
                Math.round((parseInt(healthData.overallAutomation.find((item: any) => item.type === 'automation')?.count || '0') / 
                healthData.overallAutomation.reduce((sum: number, item: any) => sum + parseInt(item.count), 0)) * 100) >= 70" 
              class="recommendation-item success"
            >
              <span class="recommendation-icon">✅</span>
              <span>Excellent automation coverage! Organization is following best practices.</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.organization-testing-health {
  .health-header {
    margin-bottom: 32px;

    h2 {
      font-size: 24px;
      font-weight: 700;
      color: #111827;
      margin: 0 0 8px 0;
    }

    p {
      color: #6b7280;
      margin: 0;
    }
  }

  .loading, .error {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .error {
    color: #ef4444;
  }
}

.health-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;

  .full-width {
    grid-column: 1 / -1;
  }
}

.chart-card, .metrics-card, .recommendations-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }
}

.chart-container {
  height: 400px;
  position: relative;
}

.metrics-list {
  display: grid;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;

  .metric-label {
    font-size: 14px;
    color: #6b7280;
  }

  .metric-value {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  }
}

.recommendations-list {
  display: grid;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fef3c7;
  border-radius: 6px;
  font-size: 14px;
  color: #92400e;

  &.success {
    background: #d1fae5;
    color: #065f46;
  }

  .recommendation-icon {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .health-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 300px;
  }
}
</style>
