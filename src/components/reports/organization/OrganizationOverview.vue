<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { Doughnut, Bar, Line } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement,
} from 'chart.js';
import { useReportData } from '../../../composables/useReportData';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  PointElement,
  LineElement
);

const { 
  getCrossProjectQualityComparisonWithFallback, 
  getOrganizationTestingHealthWithFallback,
  getAITestingImpactWithFallback,
  loading, 
  error 
} = useReportData();

const qualityData = ref<any>(null);
const healthData = ref<any>(null);
const aiData = ref<any>(null);

// Chart options
const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
};

const barOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

const lineOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'top' as const,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

// Chart data
const overallAutomationData = ref({
  labels: ['Manual', 'Automation'],
  datasets: [
    {
      data: [0, 0],
      backgroundColor: ['#ef4444', '#10b981'],
      borderWidth: 0,
    },
  ],
});

const projectPassRatesData = ref({
  labels: [],
  datasets: [
    {
      data: [],
      backgroundColor: '#3b82f6',
      borderWidth: 0,
    },
  ],
});

const qualityTrendsData = ref({
  labels: [],
  datasets: [
    {
      label: 'Pass Rate %',
      data: [],
      borderColor: '#10b981',
      backgroundColor: 'rgba(16, 185, 129, 0.1)',
      tension: 0.4,
    },
  ],
});

const fetchData = async () => {
  try {
    // Fetch all organization data
    const [quality, health, ai] = await Promise.all([
      getCrossProjectQualityComparisonWithFallback(),
      getOrganizationTestingHealthWithFallback(),
      getAITestingImpactWithFallback()
    ]);

    qualityData.value = quality;
    healthData.value = health;
    aiData.value = ai;

    // Update overall automation chart
    if (health.overallAutomation) {
      const manual = health.overallAutomation.find((item: any) => item.type === 'manual')?.count || 0;
      const automation = health.overallAutomation.find((item: any) => item.type === 'automation')?.count || 0;
      
      overallAutomationData.value = {
        labels: ['Manual', 'Automation'],
        datasets: [
          {
            data: [parseInt(manual), parseInt(automation)],
            backgroundColor: ['#ef4444', '#10b981'],
            borderWidth: 0,
          },
        ],
      };
    }

    // Update project pass rates chart
    if (quality.projectPassRates) {
      projectPassRatesData.value = {
        labels: quality.projectPassRates.map((item: any) => item.projectName),
        datasets: [
          {
            data: quality.projectPassRates.map((item: any) => 
              Math.round((parseInt(item.passedTests) / parseInt(item.totalTests)) * 100)
            ),
            backgroundColor: quality.projectPassRates.map((item: any) => {
              const passRate = (parseInt(item.passedTests) / parseInt(item.totalTests)) * 100;
              if (passRate >= 90) return '#10b981';
              if (passRate >= 75) return '#f59e0b';
              return '#ef4444';
            }),
            borderWidth: 0,
          },
        ],
      };
    }

    // Update quality trends chart
    if (health.qualityTrends) {
      qualityTrendsData.value = {
        labels: health.qualityTrends.map((item: any) => 
          new Date(item.week).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })
        ),
        datasets: [
          {
            label: 'Pass Rate %',
            data: health.qualityTrends.map((item: any) => 
              Math.round((parseInt(item.passedTests) / parseInt(item.totalTests)) * 100)
            ),
            borderColor: '#10b981',
            backgroundColor: 'rgba(16, 185, 129, 0.1)',
            tension: 0.4,
          },
        ],
      };
    }
  } catch (err) {
    console.error('Failed to fetch organization overview data:', err);
  }
};

// Calculate organization stats
const calculateOrganizationStats = () => {
  if (!qualityData.value || !healthData.value) return { projects: 0, totalTests: 0, automationRate: 0, avgPassRate: 0 };

  const projects = qualityData.value.projectCoverage?.length || 0;
  const totalTests = qualityData.value.projectCoverage?.reduce((sum: number, item: any) => sum + parseInt(item.totalTestCases), 0) || 0;
  
  const automation = healthData.value.overallAutomation?.find((item: any) => item.type === 'automation')?.count || 0;
  const manual = healthData.value.overallAutomation?.find((item: any) => item.type === 'manual')?.count || 0;
  const automationRate = (parseInt(automation) + parseInt(manual)) > 0 ? 
    Math.round((parseInt(automation) / (parseInt(automation) + parseInt(manual))) * 100) : 0;

  const avgPassRate = qualityData.value.projectPassRates?.length > 0 ?
    Math.round(qualityData.value.projectPassRates.reduce((sum: number, item: any) => 
      sum + (parseInt(item.passedTests) / parseInt(item.totalTests)) * 100, 0) / qualityData.value.projectPassRates.length) : 0;

  return { projects, totalTests, automationRate, avgPassRate };
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="organization-overview">
    <div v-if="loading" class="loading">
      Loading organization overview...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else class="overview-content">
      <div class="overview-grid">
        <!-- Organization Stats -->
        <div class="stats-card full-width">
          <h3>Organization Summary</h3>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-value">{{ calculateOrganizationStats().projects }}</div>
              <div class="stat-label">Active Projects</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ calculateOrganizationStats().totalTests }}</div>
              <div class="stat-label">Total Test Cases</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ calculateOrganizationStats().automationRate }}%</div>
              <div class="stat-label">Automation Rate</div>
            </div>
            <div class="stat-item">
              <div class="stat-value">{{ calculateOrganizationStats().avgPassRate }}%</div>
              <div class="stat-label">Avg Pass Rate</div>
            </div>
          </div>
        </div>

        <!-- Overall Automation -->
        <div class="chart-card">
          <h3>Overall Test Automation</h3>
          <div class="chart-container">
            <Doughnut :data="overallAutomationData" :options="doughnutOptions" />
          </div>
        </div>

        <!-- Project Pass Rates -->
        <div class="chart-card">
          <h3>Project Pass Rates</h3>
          <div class="chart-container">
            <Bar :data="projectPassRatesData" :options="barOptions" />
          </div>
        </div>

        <!-- Quality Trends -->
        <div class="chart-card full-width">
          <h3>Organization Quality Trends</h3>
          <div class="chart-container">
            <Line :data="qualityTrendsData" :options="lineOptions" />
          </div>
        </div>

        <!-- AI Impact Summary -->
        <div class="ai-summary-card">
          <h3>AI Testing Impact</h3>
          <div class="ai-stats">
            <div class="ai-stat-item">
              <div class="ai-stat-value">
                {{ aiData?.aiTestCases?.find((item: any) => item.isAIGenerated)?.count || 0 }}
              </div>
              <div class="ai-stat-label">AI-Generated Tests</div>
            </div>
            <div class="ai-stat-item">
              <div class="ai-stat-value">
                {{ 
                  aiData?.aiVsManualResults ? 
                  Math.round((parseInt(aiData.aiVsManualResults.find((item: any) => item.isAIGenerated && item.status === 'passed')?.count || '0') / 
                  aiData.aiVsManualResults.filter((item: any) => item.isAIGenerated).reduce((sum: number, item: any) => sum + parseInt(item.count), 0)) * 100) : 0 
                }}%
              </div>
              <div class="ai-stat-label">AI Test Success Rate</div>
            </div>
          </div>
        </div>

        <!-- Project Health Status -->
        <div class="health-card">
          <h3>Project Health Status</h3>
          <div class="health-list">
            <div 
              v-for="project in qualityData?.projectPassRates || []" 
              :key="project.projectName"
              class="health-item"
            >
              <div class="project-name">{{ project.projectName }}</div>
              <div class="health-indicators">
                <div 
                  class="health-indicator"
                  :class="{
                    'healthy': (parseInt(project.passedTests) / parseInt(project.totalTests)) >= 0.9,
                    'warning': (parseInt(project.passedTests) / parseInt(project.totalTests)) >= 0.75 && (parseInt(project.passedTests) / parseInt(project.totalTests)) < 0.9,
                    'critical': (parseInt(project.passedTests) / parseInt(project.totalTests)) < 0.75
                  }"
                >
                  {{ Math.round((parseInt(project.passedTests) / parseInt(project.totalTests)) * 100) }}%
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.organization-overview {
  .loading, .error {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .error {
    color: #ef4444;
  }
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;

  .full-width {
    grid-column: 1 / -1;
  }
}

.chart-card, .stats-card, .ai-summary-card, .health-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }
}

.chart-container {
  height: 300px;
  position: relative;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;

  .stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #111827;
    margin-bottom: 4px;
  }

  .stat-label {
    font-size: 12px;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.ai-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.ai-stat-item {
  text-align: center;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;

  .ai-stat-value {
    font-size: 24px;
    font-weight: 700;
    color: #0369a1;
    margin-bottom: 4px;
  }

  .ai-stat-label {
    font-size: 12px;
    color: #0369a1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.health-list {
  display: grid;
  gap: 12px;
}

.health-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;

  .project-name {
    font-weight: 500;
    color: #111827;
  }
}

.health-indicator {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;

  &.healthy {
    background: #d1fae5;
    color: #065f46;
  }

  &.warning {
    background: #fef3c7;
    color: #92400e;
  }

  &.critical {
    background: #fee2e2;
    color: #991b1b;
  }
}

@media (max-width: 768px) {
  .overview-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 250px;
  }

  .stats-grid, .ai-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}
</style>
