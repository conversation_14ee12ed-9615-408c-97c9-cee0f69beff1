<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { <PERSON>hnut, Bar } from 'vue-chartjs';
import {
  Chart as ChartJS,
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
} from 'chart.js';
import { useReportData } from '../../../composables/useReportData';

// Register Chart.js components
ChartJS.register(
  Title,
  Tooltip,
  Legend,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement
);

interface Props {
  projectId: string;
}

const props = defineProps<Props>();
const { getProjectExecutionEfficiencyWithFallback, loading, error } = useReportData();

const efficiencyData = ref<any>(null);

// Chart options
const doughnutOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      position: 'bottom' as const,
    },
  },
};

const barOptions = {
  responsive: true,
  maintainAspectRatio: false,
  plugins: {
    legend: {
      display: false,
    },
  },
  scales: {
    y: {
      beginAtZero: true,
    },
  },
};

// Chart data
const automationStatsData = ref({
  labels: ['Manual', 'Automation'],
  datasets: [
    {
      data: [0, 0],
      backgroundColor: ['#ef4444', '#10b981'],
      borderWidth: 0,
    },
  ],
});

const executionFrequencyData = ref({
  labels: [],
  datasets: [
    {
      data: [],
      backgroundColor: '#3b82f6',
      borderWidth: 0,
    },
  ],
});

const fetchData = async () => {
  try {
    const efficiency = await getProjectExecutionEfficiencyWithFallback(props.projectId);
    efficiencyData.value = efficiency;

    // Update automation stats
    if (efficiency.automationStats) {
      const manual = efficiency.automationStats.find((item: any) => item.type === 'manual')?.count || 0;
      const automation = efficiency.automationStats.find((item: any) => item.type === 'automation')?.count || 0;
      
      automationStatsData.value = {
        labels: ['Manual', 'Automation'],
        datasets: [
          {
            data: [parseInt(manual), parseInt(automation)],
            backgroundColor: ['#ef4444', '#10b981'],
            borderWidth: 0,
          },
        ],
      };
    }

    // Update execution frequency
    if (efficiency.executionFrequency) {
      const topTests = efficiency.executionFrequency.slice(0, 10); // Show top 10 most executed tests
      
      executionFrequencyData.value = {
        labels: topTests.map((item: any) => item.testCaseTitle.length > 30 ? 
          item.testCaseTitle.substring(0, 30) + '...' : item.testCaseTitle),
        datasets: [
          {
            data: topTests.map((item: any) => parseInt(item.executionCount)),
            backgroundColor: topTests.map((item: any) => 
              item.testCaseType === 'automation' ? '#10b981' : '#ef4444'
            ),
            borderWidth: 0,
          },
        ],
      };
    }
  } catch (err) {
    console.error('Failed to fetch execution efficiency data:', err);
  }
};

// Calculate time savings (mock calculation)
const calculateTimeSavings = () => {
  if (!efficiencyData.value?.automationStats) return { saved: 0, percentage: 0 };
  
  const automation = efficiencyData.value.automationStats.find((item: any) => item.type === 'automation')?.count || 0;
  const manual = efficiencyData.value.automationStats.find((item: any) => item.type === 'manual')?.count || 0;
  
  // Assume each automated test saves 10 minutes compared to manual execution
  const timeSavedMinutes = parseInt(automation) * 10;
  const totalTestTime = (parseInt(automation) + parseInt(manual)) * 15; // Assume 15 min per test
  const percentage = totalTestTime > 0 ? Math.round((timeSavedMinutes / totalTestTime) * 100) : 0;
  
  return { saved: timeSavedMinutes, percentage };
};

onMounted(() => {
  fetchData();
});
</script>

<template>
  <div class="execution-efficiency">
    <div class="efficiency-header">
      <h2>Execution Efficiency</h2>
      <p>Analysis of test execution efficiency and automation impact</p>
    </div>

    <div v-if="loading" class="loading">
      Loading execution efficiency data...
    </div>

    <div v-else-if="error" class="error">
      {{ error }}
    </div>

    <div v-else class="efficiency-content">
      <div class="efficiency-grid">
        <!-- Time Savings Summary -->
        <div class="summary-card">
          <h3>Time Savings Through Automation</h3>
          <div class="savings-stats">
            <div class="savings-item">
              <div class="savings-value">{{ calculateTimeSavings().saved }}</div>
              <div class="savings-label">Minutes Saved</div>
            </div>
            <div class="savings-item">
              <div class="savings-value">{{ calculateTimeSavings().percentage }}%</div>
              <div class="savings-label">Efficiency Gain</div>
            </div>
            <div class="savings-item">
              <div class="savings-value">
                {{ Math.round(calculateTimeSavings().saved / 60) }}
              </div>
              <div class="savings-label">Hours Saved</div>
            </div>
          </div>
        </div>

        <!-- Automation vs Manual -->
        <div class="chart-card">
          <h3>Automation vs Manual Testing</h3>
          <div class="chart-container">
            <Doughnut :data="automationStatsData" :options="doughnutOptions" />
          </div>
          <div class="chart-summary">
            <div class="summary-item">
              <span class="summary-label">Automation Rate:</span>
              <span class="summary-value">
                {{ 
                  efficiencyData?.automationStats ? 
                  Math.round((parseInt(efficiencyData.automationStats.find((item: any) => item.type === 'automation')?.count || '0') / 
                  efficiencyData.automationStats.reduce((sum: number, item: any) => sum + parseInt(item.count), 0)) * 100) : 0 
                }}%
              </span>
            </div>
          </div>
        </div>

        <!-- Test Execution Frequency -->
        <div class="chart-card full-width">
          <h3>Most Frequently Executed Tests</h3>
          <div class="chart-container">
            <Bar :data="executionFrequencyData" :options="barOptions" />
          </div>
          <div class="frequency-legend">
            <div class="legend-item">
              <span class="legend-color automated"></span>
              <span>Automated Tests</span>
            </div>
            <div class="legend-item">
              <span class="legend-color manual"></span>
              <span>Manual Tests</span>
            </div>
          </div>
        </div>

        <!-- Efficiency Metrics -->
        <div class="metrics-card">
          <h3>Efficiency Metrics</h3>
          <div class="metrics-list">
            <div class="metric-item">
              <div class="metric-label">Total Test Cases</div>
              <div class="metric-value">
                {{ 
                  efficiencyData?.automationStats ? 
                  efficiencyData.automationStats.reduce((sum: number, item: any) => sum + parseInt(item.count), 0) : 0 
                }}
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Automated Test Cases</div>
              <div class="metric-value">
                {{ efficiencyData?.automationStats?.find((item: any) => item.type === 'automation')?.count || 0 }}
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Most Executed Test</div>
              <div class="metric-value">
                {{ 
                  efficiencyData?.executionFrequency?.length > 0 ? 
                  efficiencyData.executionFrequency[0].executionCount + ' runs' : 'N/A' 
                }}
              </div>
            </div>
            <div class="metric-item">
              <div class="metric-label">Avg Executions per Test</div>
              <div class="metric-value">
                {{ 
                  efficiencyData?.executionFrequency?.length > 0 ? 
                  Math.round(efficiencyData.executionFrequency.reduce((sum: number, item: any) => sum + parseInt(item.executionCount), 0) / efficiencyData.executionFrequency.length) : 0 
                }}
              </div>
            </div>
          </div>
        </div>

        <!-- Recommendations -->
        <div class="recommendations-card">
          <h3>Efficiency Recommendations</h3>
          <div class="recommendations-list">
            <div 
              v-if="calculateTimeSavings().percentage < 50" 
              class="recommendation-item"
            >
              <span class="recommendation-icon">⚡</span>
              <span>Consider automating more test cases to increase efficiency</span>
            </div>
            <div 
              v-if="efficiencyData?.executionFrequency?.some((item: any) => item.testCaseType === 'manual' && parseInt(item.executionCount) > 5)" 
              class="recommendation-item"
            >
              <span class="recommendation-icon">🤖</span>
              <span>Frequently executed manual tests should be automated</span>
            </div>
            <div 
              v-if="calculateTimeSavings().percentage >= 70" 
              class="recommendation-item success"
            >
              <span class="recommendation-icon">✅</span>
              <span>Excellent automation coverage! Keep up the good work</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.execution-efficiency {
  .efficiency-header {
    margin-bottom: 32px;

    h2 {
      font-size: 24px;
      font-weight: 700;
      color: #111827;
      margin: 0 0 8px 0;
    }

    p {
      color: #6b7280;
      margin: 0;
    }
  }

  .loading, .error {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .error {
    color: #ef4444;
  }
}

.efficiency-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;

  .full-width {
    grid-column: 1 / -1;
  }
}

.chart-card, .summary-card, .metrics-card, .recommendations-card {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0 0 20px 0;
    font-size: 18px;
    font-weight: 600;
    color: #111827;
  }
}

.chart-container {
  height: 300px;
  position: relative;
}

.chart-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .summary-label {
    font-size: 14px;
    color: #6b7280;
  }

  .summary-value {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  }
}

.savings-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.savings-item {
  text-align: center;
  padding: 16px;
  background: #f0f9ff;
  border-radius: 8px;

  .savings-value {
    font-size: 24px;
    font-weight: 700;
    color: #0369a1;
    margin-bottom: 4px;
  }

  .savings-label {
    font-size: 12px;
    color: #0369a1;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }
}

.frequency-legend {
  display: flex;
  gap: 16px;
  margin-top: 16px;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #6b7280;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 4px;

  &.automated {
    background-color: #10b981;
  }

  &.manual {
    background-color: #ef4444;
  }
}

.metrics-list {
  display: grid;
  gap: 12px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f9fafb;
  border-radius: 6px;

  .metric-label {
    font-size: 14px;
    color: #6b7280;
  }

  .metric-value {
    font-size: 16px;
    font-weight: 600;
    color: #111827;
  }
}

.recommendations-list {
  display: grid;
  gap: 12px;
}

.recommendation-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #fef3c7;
  border-radius: 6px;
  font-size: 14px;
  color: #92400e;

  &.success {
    background: #d1fae5;
    color: #065f46;
  }

  .recommendation-icon {
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .efficiency-grid {
    grid-template-columns: 1fr;
  }

  .chart-container {
    height: 250px;
  }

  .savings-stats {
    grid-template-columns: 1fr;
  }
}
</style>
