<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import ProjectOverview from './project/ProjectOverview.vue';
import TestCaseAnalytics from './project/TestCaseAnalytics.vue';
import TestRunPerformance from './project/TestRunPerformance.vue';
import ExecutionEfficiency from './project/ExecutionEfficiency.vue';
import OrganizationOverview from './organization/OrganizationOverview.vue';
import ProjectQualityComparison from './organization/ProjectQualityComparison.vue';
import OrganizationTestingHealth from './organization/OrganizationTestingHealth.vue';
import AITestingImpact from './organization/AITestingImpact.vue';

const route = useRoute();

// Determine if we're in project-specific or cross-project mode
const isProjectSpecific = computed(() => !!route.params.id);
const projectId = computed(() => route.params.id as string);

const activeTab = ref('overview');

const tabs = computed(() => {
  if (isProjectSpecific.value) {
    return [
      { id: 'overview', name: 'Overview', icon: '📊' },
      { id: 'test-cases', name: 'Test Case Analytics', icon: '📝' },
      { id: 'test-runs', name: 'Test Run Performance', icon: '▶️' },
      { id: 'efficiency', name: 'Execution Efficiency', icon: '⚡' }
    ];
  } else {
    return [
      { id: 'overview', name: 'Overview', icon: '📊' },
      { id: 'quality', name: 'Project Quality', icon: '🎯' },
      { id: 'health', name: 'Testing Health', icon: '💚' },
      { id: 'ai-impact', name: 'AI Impact', icon: '🤖' }
    ];
  }
});

const setActiveTab = (tabId: string) => {
  activeTab.value = tabId;
};

onMounted(() => {
  // Set default tab
  activeTab.value = 'overview';
});
</script>

<template>
  <div class="report-dashboard">
    <div class="report-header">
      <h1 v-if="isProjectSpecific">Project Reports</h1>
      <h1 v-else>Organization Reports</h1>
      <p v-if="isProjectSpecific" class="report-subtitle">
        Analytics and insights for this project
      </p>
      <p v-else class="report-subtitle">
        Cross-project analytics and organization-wide insights
      </p>
    </div>

    <div class="report-tabs">
      <button
        v-for="tab in tabs"
        :key="tab.id"
        class="tab-button"
        :class="{ active: activeTab === tab.id }"
        @click="setActiveTab(tab.id)"
      >
        <span class="tab-icon">{{ tab.icon }}</span>
        <span class="tab-name">{{ tab.name }}</span>
      </button>
    </div>

    <div class="report-content">
      <!-- Project-specific reports -->
      <template v-if="isProjectSpecific">
        <div v-if="activeTab === 'overview'" class="tab-content">
          <ProjectOverview :project-id="projectId" />
        </div>
        <div v-if="activeTab === 'test-cases'" class="tab-content">
          <TestCaseAnalytics :project-id="projectId" />
        </div>
        <div v-if="activeTab === 'test-runs'" class="tab-content">
          <TestRunPerformance :project-id="projectId" />
        </div>
        <div v-if="activeTab === 'efficiency'" class="tab-content">
          <ExecutionEfficiency :project-id="projectId" />
        </div>
      </template>

      <!-- Cross-project reports -->
      <template v-else>
        <div v-if="activeTab === 'overview'" class="tab-content">
          <OrganizationOverview />
        </div>
        <div v-if="activeTab === 'quality'" class="tab-content">
          <ProjectQualityComparison />
        </div>
        <div v-if="activeTab === 'health'" class="tab-content">
          <OrganizationTestingHealth />
        </div>
        <div v-if="activeTab === 'ai-impact'" class="tab-content">
          <AITestingImpact />
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.report-dashboard {
  padding: 24px;
  background-color: #f9fafb;
  min-height: 100vh;
}

.report-header {
  margin-bottom: 32px;

  h1 {
    font-size: 32px;
    font-weight: 700;
    color: #111827;
    margin: 0 0 8px 0;
  }

  .report-subtitle {
    font-size: 16px;
    color: #6b7280;
    margin: 0;
  }
}

.report-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 32px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 0;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: none;
  border: none;
  border-bottom: 3px solid transparent;
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    color: #374151;
    background-color: #f3f4f6;
  }

  &.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background-color: #eff6ff;
  }

  .tab-icon {
    font-size: 16px;
  }

  .tab-name {
    white-space: nowrap;
  }
}

.report-content {
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.tab-content {
  padding: 32px;
}

@media (max-width: 768px) {
  .report-dashboard {
    padding: 16px;
  }

  .report-tabs {
    flex-wrap: wrap;
    gap: 4px;
  }

  .tab-button {
    padding: 8px 12px;
    font-size: 12px;

    .tab-name {
      display: none;
    }
  }

  .tab-content {
    padding: 20px;
  }
}
</style>
