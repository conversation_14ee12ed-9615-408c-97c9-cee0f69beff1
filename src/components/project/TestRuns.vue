<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import TestRunsTable from '../project/test_runs/TestRunsTable.vue';
import TestRunModal from '../project/test_runs/TestRunModal.vue';
import TestRunDastModal from '../project/test_runs/TestRunDastModal.vue';
import ConfirmationModal from '../common/ConfirmationModal.vue';

interface TestRun {
  id: string;
  name: string;
  description: string;
  startTime: string | null;
  endTime: string | null;
  environment: string | null;
  build: string | null;
  release: string | null;
  selectionType: string;
  type: string;
  createdAt: string;
  updatedAt: string;
}

const route = useRoute();
const router = useRouter();
const projectId = route.params.id as string;

const testRuns = ref<TestRun[]>([]);
const loading = ref(false);
const searchQuery = ref('');
const selectedTestRuns = ref<string[]>([]);
const currentPage = ref(1);
const totalPages = ref(1);
const totalItems = ref(0);
const error = ref('');

// Modal states
const showCreateModal = ref(false);
const showCreateDastModal = ref(false);
const showDeleteModal = ref(false);
const editingTestRun = ref<TestRun | null>(null);
const deletingTestRun = ref<TestRun | null>(null);

// Dropdown state
const showDropdown = ref(false);

const bulkDeleteInProgress = ref(false);
const bulkDeleteProgress = ref(0);
const bulkDeleteTotal = ref(0);

const fetchTestRuns = async () => {
  try {
    loading.value = true;
    error.value = '';

    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs`,
      {
        params: {
          page: currentPage.value,
          limit: 50,
          search: searchQuery.value || undefined
        }
      }
    );

    testRuns.value = response.data.testRuns;
    totalPages.value = response.data.totalPages;
    totalItems.value = response.data.total;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch test runs';
  } finally {
    loading.value = false;
  }
};

const handleSearch = () => {
  currentPage.value = 1;
  fetchTestRuns();
};

const handleCreateTestRun = () => {
  showDropdown.value = !showDropdown.value;
};

const handleCreateGeneralTestRun = () => {
  editingTestRun.value = null;
  showCreateModal.value = true;
  showDropdown.value = false;
};

const handleCreateDastTestRun = () => {
  editingTestRun.value = null;
  showCreateDastModal.value = true;
  showDropdown.value = false;
};

const handleEditTestRun = (testRun: TestRun) => {
  editingTestRun.value = testRun;

  // Check test run type to determine which modal to open
  if (testRun.type === 'security-dast') {
    console.log(`Opening DAST modal for editing test run: ${testRun.name} (type: ${testRun.type})`);
    showCreateDastModal.value = true;
  } else {
    console.log(`Opening general modal for editing test run: ${testRun.name} (type: ${testRun.type})`);
    showCreateModal.value = true;
  }
};

const handleDeleteTestRun = (testRun: TestRun) => {
  deletingTestRun.value = testRun;
  showDeleteModal.value = true;
};

const handleBulkDelete = () => {
  if (selectedTestRuns.value.length > 0) {
    showDeleteModal.value = true;
  }
};

const handleDeleteConfirm = async () => {
  try {
    loading.value = true;
    error.value = '';

    if (deletingTestRun.value) {
      // Single delete - use existing endpoint
      await axios.delete(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${deletingTestRun.value.id}`
      );
    } else {
      // Bulk delete with progress tracking
      bulkDeleteInProgress.value = true;
      bulkDeleteTotal.value = selectedTestRuns.value.length;
      
      // Use batch size of 50 for better performance
      const batchSize = 50;
      const idsToDelete = [...selectedTestRuns.value];
      
      for (let i = 0; i < idsToDelete.length; i += batchSize) {
        const batchIds = idsToDelete.slice(i, i + batchSize);
        
        // Call the new batch delete endpoint
        await axios.delete(
          `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/batch`,
          { data: { ids: batchIds } }
        );
        
        // Update progress after each batch
        bulkDeleteProgress.value = Math.min(i + batchSize, idsToDelete.length);
      }
      
      bulkDeleteInProgress.value = false;
    }

    await fetchTestRuns();
    showDeleteModal.value = false;
    deletingTestRun.value = null;
    selectedTestRuns.value = [];
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to delete test run(s)';
    bulkDeleteInProgress.value = false;
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async (formData: any) => {
  try {
    loading.value = true;
    error.value = '';

    if (editingTestRun.value) {
      await axios.patch(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${editingTestRun.value.id}`,
        formData
      );
    } else {
      await axios.post(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs`,
        formData
      );
    }

    await fetchTestRuns();
    showCreateModal.value = false;
    editingTestRun.value = null;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to save test run';
  } finally {
    loading.value = false;
  }
};

const handleDastSubmit = async (formData: any) => {
  try {
    loading.value = true;
    error.value = '';

    if (editingTestRun.value) {
      await axios.patch(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${editingTestRun.value.id}`,
        formData
      );
    } else {
      await axios.post(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs`,
        formData
      );
    }

    await fetchTestRuns();
    showCreateDastModal.value = false;
    editingTestRun.value = null;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to save DAST test run';
  } finally {
    loading.value = false;
  }
};

const changePage = (page: number) => {
  currentPage.value = page;
  fetchTestRuns();
};
const handleViewTestRun = (testRun: TestRun) => {
  router.push(`/projects/${projectId}/test-runs/${testRun.id}`);
};

// Close dropdown when clicking outside
const closeDropdown = (event: Event) => {
  const target = event.target as HTMLElement;
  const dropdown = target.closest('.create-dropdown');
  if (!dropdown) {
    showDropdown.value = false;
  }
};

onMounted(() => {
  fetchTestRuns();
  document.addEventListener('click', closeDropdown);
});

// Clean up event listener
onUnmounted(() => {
  document.removeEventListener('click', closeDropdown);
});
</script>

<template>
  <div class="test-runs">
    <div class="header">
      <div class="title-section">
        <h2>Test Runs</h2>
        <span class="test-count" v-if="!loading">
          {{ totalItems }} test runs
        </span>
      </div>
      <div class="actions">
        <div class="search-box">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search test runs..."
            @keyup.enter="handleSearch"
          />
          <button class="search-button" @click="handleSearch">
            Search
          </button>
        </div>

        <button
          v-if="selectedTestRuns.length > 0"
          class="delete-button"
          @click="handleBulkDelete"
        >
          Delete Selected
        </button>

        <div class="create-dropdown">
          <button class="create-button" @click="handleCreateTestRun">
            Create Test Run
            <span class="dropdown-arrow">▼</span>
          </button>

          <div v-if="showDropdown" class="dropdown-menu">
            <button class="dropdown-item" @click="handleCreateGeneralTestRun">
              General
            </button>
            <button class="dropdown-item" @click="handleCreateDastTestRun">
              Security Test - DAST
            </button>
          </div>
        </div>
      </div>
    </div>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <!-- Test Runs Table -->
    <TestRunsTable
      :test-runs="testRuns"
      :loading="loading"
      :selected-test-runs="selectedTestRuns"
      @update:selected-test-runs="selectedTestRuns = $event"
      @view="handleViewTestRun"
      @edit="handleEditTestRun"
      @delete="handleDeleteTestRun"
    />

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="pagination">
      <button 
        class="page-button"
        :disabled="currentPage === 1"
        @click="changePage(currentPage - 1)"
      >
        Previous
      </button>
      <span class="page-info">
        Page {{ currentPage }} of {{ totalPages }}
      </span>
      <button 
        class="page-button"
        :disabled="currentPage === totalPages"
        @click="changePage(currentPage + 1)"
      >
        Next
      </button>
    </div>

    <!-- Create/Edit Modal -->
    <TestRunModal
      :show="showCreateModal"
      :editing-test-run="editingTestRun"
      :loading="loading"
      @close="showCreateModal = false; editingTestRun = null"
      @submit="handleSubmit"
    />

    <!-- Create/Edit DAST Modal -->
    <TestRunDastModal
      :show="showCreateDastModal"
      :editing-test-run="editingTestRun"
      :loading="loading"
      @close="showCreateDastModal = false; editingTestRun = null"
      @submit="handleDastSubmit"
    />

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      :is-open="showDeleteModal"
      title="Delete Test Run"
      :message="deletingTestRun 
        ? `Are you sure you want to delete test run '${deletingTestRun.name}'?`
        : `Are you sure you want to delete ${selectedTestRuns.length} selected test runs?`"
      :isProcessing="bulkDeleteInProgress"
      :progressValue="(bulkDeleteProgress / bulkDeleteTotal) * 100"
      :progressMessage="`Deleting ${bulkDeleteProgress} of ${bulkDeleteTotal}`"
      @confirm="handleDeleteConfirm"
      @cancel="() => { showDeleteModal = false; deletingTestRun = null; }"
    />
  </div>
</template>

<style lang="scss" scoped>
.test-runs {
  padding: 20px;
}

.header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  align-items: baseline;
  gap: 12px;

  h2 {
    margin: 0;
    font-size: 24px;
    color: #374151;
  }

  .test-count {
    color: #6b7280;
    font-size: 14px;
  }
}

.actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.search-box {
  display: flex;
  gap: 8px;

  input {
    width: 300px;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }

  .search-button {
    padding: 8px 16px;
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: #e5e7eb;
    }
  }
}

.delete-button {
  padding: 8px 16px;
  background-color: #ef4444;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;

  &:hover:not(:disabled) {
    background-color: #dc2626;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.create-dropdown {
  position: relative;
  display: inline-block;
}

.create-button {
  padding: 8px 16px;
  background-color: #0aa957;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;

  &:hover {
    background-color: #056c37;
  }

  .dropdown-arrow {
    font-size: 12px;
    transition: transform 0.2s;
  }
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 200px;
  margin-top: 4px;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 12px 16px;
  text-align: left;
  background: none;
  border: none;
  color: #374151;
  font-size: 14px;
  cursor: pointer;

  &:hover {
    background-color: #f9fafb;
  }

  &:first-child {
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;
  }

  &:last-child {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;

  .page-button {
    padding: 8px 16px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background-color: white;
    color: #374151;
    cursor: pointer;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &:hover:not(:disabled) {
      background-color: #f9fafb;
    }
  }

  .page-info {
    color: #6b7280;
  }
}
</style>