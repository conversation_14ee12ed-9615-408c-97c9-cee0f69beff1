<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import FolderTree from './test_cases/FolderTree.vue';
import TestCasesTable from './test_cases/TestCaseTable.vue';
import TestCaseModal from './test_cases/TestCaseModal.vue';
import AIGenerateTestCase from './test_cases/AIGenerateTestCase.vue';
import ConfirmationModal from '../common/ConfirmationModal.vue';
import ImportTestCaseModal from '../project/test_cases/ImportTestCaseModal.vue';
import ExportTestCaseModal from '../project/test_cases/ExportTestCaseModal.vue';
import MoveFolderModal from '../project/test_cases/MoveFolderModal.vue';
import FilterTestCasesModal from '../project/test_cases/FilterTestCasesModal.vue';
import ActiveTestCaseFilters from '../project/test_cases/ActiveTestCaseFilters.vue';
import { TestCase, Tag, Folder } from '../../types';

const route = useRoute();
const projectId = route.params.id as string;

// Pagination state
const testCases = ref<TestCase[]>([]);
const totalItems = ref(0);
const currentPage = ref(1);
const itemsPerPage = ref(50);

const selectedFolderId = ref<string | undefined>(undefined);
const selectedFolderName = ref<string | undefined>(undefined);
const showingAllTestCases = ref(true);
const loading = ref(false);
const error = ref('');
const searchQuery = ref('');
const selectedTestCases = ref<string[]>([]);
const showImportModal = ref(false);
const showExportModal = ref(false);

// Sorting state
const sortField = ref('tcId');
const sortDirection = ref('asc');

// Filter modal state
const showFilterModal = ref(false);
const filters = ref({
  priority: [] as string[],
  platform: [] as string[],
  testCaseType: [] as string[],
  type: [] as string[],
  tagIds: [] as string[]
});

// Temporary filters for the modal
const tempFilters = ref({
  priority: [] as string[],
  platform: [] as string[],
  testCaseType: [] as string[],
  type: [] as string[],
  tagIds: [] as string[]
});

// Filter options with correct case for enum values
const priorityOptions = ref(['low', 'medium', 'high', 'critical']);
const platformOptions = ref(['web', 'mobile', 'api', 'desktop']);
const testCaseTypeOptions = ref(['manual', 'automation']);
const typeOptions = ref<string[]>([]);
const tagOptions = ref<Tag[]>([]);

// Modal states
const showCreateModal = ref(false);
const showGenerateModal = ref(false);
const showDeleteModal = ref(false);
const editingTestCase = ref<TestCase | null>(null);
const deletingTestCase = ref<TestCase | null>(null);
const modalKey = ref(0);

const showViewModal = ref(false);
const viewTestCase = ref<TestCase | null>(null);

const isDeleting = ref(false);
const deleteProgress = ref(0);
const deleteProgressMessage = ref('');

const isImporting = ref(false);
const importProgress = ref(0);
const importProgressMessage = ref('');
const importError = ref<string | null>(null);

// Folders state
const folders = ref<Folder[]>([]);
const showMoveFolderModal = ref(false);
const isMoving = ref(false);

const emit = defineEmits<{
  'close': [];
  'import': [file: File];
  'refresh': [];
}>();

const handleMoveTestCases = async (targetFolderId: string | null) => {
  try {
    isMoving.value = true;
    error.value = '';

    // Move each selected test case to the target folder
    for (const testCaseId of selectedTestCases.value) {
      await axios.patch(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${testCaseId}`,
        { folderId: targetFolderId }
      );
    }

    // Refresh the test cases list
    await fetchTestCases();
    selectedTestCases.value = []; // Clear selections after moving
    showMoveFolderModal.value = false;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to move test cases';
  } finally {
    isMoving.value = false;
  }
};

const handleViewTestCase = (testCase: TestCase) => {
  viewTestCase.value = testCase;
  showViewModal.value = true;
  showCreateModal.value = true;
};

const openCreateModal = () => {
  editingTestCase.value = null;
  showCreateModal.value = true;
  modalKey.value++;
};

const handleOpenCreateModal = () => {
  fetchFolders();
  openCreateModal();
};

const handleFolderSelect = (item: { id: string; name: string }) => {
  selectedFolderId.value = item.id;
  selectedFolderName.value = item.name;
  showingAllTestCases.value = false;
  currentPage.value = 1; // Reset to first page when changing folders
  fetchTestCases();
};

const showAllTestCases = () => {
  showingAllTestCases.value = true;
  selectedFolderId.value = undefined;
  selectedFolderName.value = undefined;
  currentPage.value = 1; // Reset to first page when showing all
  fetchTestCases();
};

const filteredTestCases = computed(() => {
  const searchTerm = searchQuery.value.toLowerCase();
  let filtered = testCases.value;

  if (!showingAllTestCases.value) {
    if (selectedFolderId.value) {
      filtered = filtered.filter(tc => tc.folderId === selectedFolderId.value);
    } else {
      filtered = filtered.filter(tc => !tc.folderId);
    }
  }

  if (searchTerm) {
    filtered = filtered.filter(tc => {
      // Check for TC-ID matches (supports both "TC-123" and "123" formats)
      const tcIdMatch = searchTerm.startsWith('tc-')
        ? `tc-${tc.tcId}`.toLowerCase() === searchTerm
        : tc.tcId.toString().includes(searchTerm);

      // Check for tag matches
      const tagMatch = tc.tags && tc.tags.some(tag =>
        tag.name.toLowerCase().includes(searchTerm)
      );

      return tcIdMatch ||
        tagMatch ||
        tc.title.toLowerCase().includes(searchTerm) ||
        tc.type.toLowerCase().includes(searchTerm) ||
        tc.priority.toLowerCase().includes(searchTerm) ||
        tc.platform.toLowerCase().includes(searchTerm) ||
        tc.testCaseType.toLowerCase().includes(searchTerm);
    });
  }

  return filtered;
});

const handleSearch = () => {
  currentPage.value = 1; // Reset to first page when searching
  fetchTestCases();
};

// Handle sorting
const handleSort = (field: string) => {
  if (sortField.value === field) {
    // Toggle direction if clicking the same field
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    // Default to ascending for a new sort field
    sortField.value = field;
    sortDirection.value = 'asc';
  }
  currentPage.value = 1;
  fetchTestCases();
};

// Open filter modal
const openFilterModal = async () => {
  // Copy current filters to temp filters
  tempFilters.value = {
    priority: [...filters.value.priority],
    platform: [...filters.value.platform],
    testCaseType: [...filters.value.testCaseType],
    type: [...filters.value.type],
    tagIds: [...filters.value.tagIds]
  };

  // Fetch all available filter options
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/filter-options`
    );

    // Update type options with all available types
    if (response.data.types && response.data.types.length > 0) {
      typeOptions.value = response.data.types;
    }

    // Update tag options with all available tags
    if (response.data.tags && response.data.tags.length > 0) {
      tagOptions.value = response.data.tags;
    }
  } catch (err) {
    console.error('Failed to fetch filter options:', err);
  }

  showFilterModal.value = true;
};

// Handle apply filters from modal component
const handleApplyFilters = (newFilters: typeof filters.value) => {
  filters.value = newFilters;
  currentPage.value = 1;
  fetchTestCases();
};

// Reset and apply filters
const resetAndApplyFilters = () => {
  filters.value = {
    priority: [],
    platform: [],
    testCaseType: [],
    type: [],
    tagIds: []
  };
  tempFilters.value = {
    priority: [],
    platform: [],
    testCaseType: [],
    type: [],
    tagIds: []
  };
  currentPage.value = 1;
  fetchTestCases();
};

// Get active filter count
const getActiveFilterCount = () => {
  let count = 0;
  if (filters.value.priority.length > 0) count += filters.value.priority.length;
  if (filters.value.platform.length > 0) count += filters.value.platform.length;
  if (filters.value.testCaseType.length > 0) count += filters.value.testCaseType.length;
  if (filters.value.type.length > 0) count += filters.value.type.length;
  if (filters.value.tagIds.length > 0) count += filters.value.tagIds.length;
  return count;
};

const handlePageChange = (page: number) => {
  currentPage.value = page;
  fetchTestCases();
};

// Computed property for the filter button text
const filterButtonText = computed(() => {
  if (showingAllTestCases.value) {
    return 'Show All';
  } else {
    return selectedFolderName.value
      ? `Filtered = <span class="folder-name">${selectedFolderName.value}</span>`
      : 'Filtered';
  }
});

const clearFilter = () => {
  selectedFolderId.value = undefined;
  selectedFolderName.value = undefined;
  showingAllTestCases.value = true;
  currentPage.value = 1; // Reset to first page when clearing filter
  fetchTestCases();
};

// Methods
const fetchTestCases = async () => {
  try {
    loading.value = true;
    selectedTestCases.value = []; // Clear selections when fetching new data

    // Build query parameters
    const params = new URLSearchParams({
      page: currentPage.value.toString(),
      limit: itemsPerPage.value.toString(),
      sortField: sortField.value,
      sortDirection: sortDirection.value.toUpperCase()
    });

    if (searchQuery.value) {
      params.append('search', searchQuery.value);
    }

    if (!showingAllTestCases.value && selectedFolderId.value) {
      params.append('folderId', selectedFolderId.value);
    }

    // Add advanced filters
    if (filters.value.priority.length > 0) {
      filters.value.priority.forEach(priority => {
        params.append('priorityFilter', priority);
      });
    }

    if (filters.value.platform.length > 0) {
      filters.value.platform.forEach(platform => {
        params.append('platformFilter', platform);
      });
    }

    if (filters.value.testCaseType.length > 0) {
      filters.value.testCaseType.forEach(testCaseType => {
        params.append('testCaseTypeFilter', testCaseType);
      });
    }

    if (filters.value.type.length > 0) {
      filters.value.type.forEach(type => {
        params.append('typeFilter', type);
      });
    }

    if (filters.value.tagIds.length > 0) {
      filters.value.tagIds.forEach(tagId => {
        params.append('tagFilter', tagId);
      });
    }

    console.log('Fetching test cases with params:', params.toString());

    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases?${params.toString()}`
    );

    // Update data with response
    testCases.value = response.data.data.map((tc: any) => ({
      ...tc,
      tags: tc.tags || []
    }));

    totalItems.value = response.data.total;

    // Extract unique values for filter options
    extractFilterOptions();

  } catch (err) {
    console.error('Failed to fetch test cases:', err);
    error.value = 'Failed to fetch test cases';
  } finally {
    loading.value = false;
  }
};

// Extract unique values from test cases for filter options
const extractFilterOptions = () => {
  // Only update type options if they haven't been populated from the API
  if (typeOptions.value.length === 0) {
    // Extract unique types
    const types = new Set<string>();
    testCases.value.forEach(testCase => {
      if (testCase.type) {
        types.add(testCase.type);
      }
    });
    typeOptions.value = Array.from(types);
  }

  // Only update tag options if they haven't been populated from the API
  if (tagOptions.value.length === 0) {
    // Extract unique tags
    const tags = new Map<string, Tag>();
    testCases.value.forEach(testCase => {
      if (testCase.tags && testCase.tags.length > 0) {
        testCase.tags.forEach(tag => {
          tags.set(tag.id, tag);
        });
      }
    });
    tagOptions.value = Array.from(tags.values());
  }
};

const fetchFolders = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/folders`
    );
    folders.value = response.data;
  } catch (err) {
    console.error('Failed to fetch folders:', err);
  }
};

const handleGenerateTestCases = async (generateData: any) => {
  try {
    loading.value = true;

    await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/generate`,
      generateData
    );

    await fetchTestCases();
    showGenerateModal.value = false;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to generate test cases';
  } finally {
    loading.value = false;
  }
};

const handleEditTestCase = (testCase: TestCase) => {
  fetchFolders();
  editingTestCase.value = testCase;
  showCreateModal.value = true;
};

const handleDeleteTestCase = (testCase: TestCase) => {
  selectedTestCases.value = [];
  deletingTestCase.value = testCase;
  showDeleteModal.value = true;
};

const handleBulkDelete = () => {
  if (selectedTestCases.value.length > 0) {
    deletingTestCase.value = null; // Clear single delete context
    showDeleteModal.value = true;
  }
};

const handleDuplicateTestCases = async () => {
  try {
    loading.value = true;
    error.value = '';

    // Get the selected test cases
    const selectedCases = testCases.value.filter(tc => selectedTestCases.value.includes(tc.id));

    // Create duplicates
    for (const testCase of selectedCases) {
      const duplicateData = {
        title: `${testCase.title} (Copy)`,
        type: testCase.type,
        priority: testCase.priority,
        platform: testCase.platform,
        testCaseType: testCase.testCaseType,
        precondition: testCase.precondition,
        steps: testCase.steps,
        expectation: testCase.expectation,
        folderId: testCase.folderId,
        tags: testCase.tags?.map(tag => tag.id) || [],
        automationByAgentq: false
      };

      await axios.post(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases`,
        duplicateData
      );
    }

    // Refresh the test cases list
    await fetchTestCases();
    selectedTestCases.value = []; // Clear selections after duplication
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to duplicate test cases';
  } finally {
    loading.value = false;
  }
};

const handleDeleteConfirm = async () => {
  try {
    isDeleting.value = true;
    deleteProgress.value = 0;

    // Determine which IDs to delete
    const idsToDelete = deletingTestCase.value
      ? [deletingTestCase.value.id]
      : selectedTestCases.value;

    if (idsToDelete.length === 0) return;

    // Use bulk delete for multiple items, single delete for one item
    if (idsToDelete.length > 1) {
      // Bulk delete logic
      const BATCH_SIZE = 100;
      const totalItems = idsToDelete.length;

      for (let i = 0; i < totalItems; i += BATCH_SIZE) {
        const batch = idsToDelete.slice(i, i + BATCH_SIZE);

        await axios.post(
          `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/bulk-delete`,
          { ids: batch }
        );

        const progress = Math.round((i + batch.length) / totalItems * 100);
        deleteProgress.value = progress;
        deleteProgressMessage.value = `Deleting test cases... (${progress}%)`;
      }
    } else {
      // Single delete logic
      await axios.delete(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${idsToDelete[0]}`
      );
      deleteProgress.value = 100;
    }

    deleteProgressMessage.value = 'Refreshing test cases...';
    await fetchTestCases();
    showDeleteModal.value = false;
    deletingTestCase.value = null;
    selectedTestCases.value = [];
  } catch (err: any) {
    console.error('Failed to delete test cases:', err);
    error.value = err.response?.data?.message || 'Failed to delete test cases';
  } finally {
    isDeleting.value = false;
    deleteProgress.value = 0;
    deleteProgressMessage.value = '';
  }
};

// Function to intelligently merge test case changes with existing automation data
const mergeAutomationSteps = (testCaseData: any, existingAutomationSteps: any[]) => {
  const newSteps: any[] = [];

  // Build the expected step structure from test case data
  const expectedSteps: string[] = [];

  // Add precondition if it exists
  if (testCaseData.precondition && testCaseData.precondition.trim()) {
    expectedSteps.push(testCaseData.precondition.trim());
  }

  // Add test steps
  if (testCaseData.steps && testCaseData.steps.trim()) {
    const stepLines = testCaseData.steps.split('\n').filter((line: string) => line.trim());
    expectedSteps.push(...stepLines.map((line: string) => line.trim()));
  }

  // Add expectation if it exists
  if (testCaseData.expectation && testCaseData.expectation.trim()) {
    expectedSteps.push(testCaseData.expectation.trim());
  }

  // Create a map of existing steps by stepName for quick lookup
  const existingStepMap = new Map();
  existingAutomationSteps.forEach(step => {
    existingStepMap.set(step.stepName, step);
  });

  // Process each expected step
  expectedSteps.forEach((expectedStepName, index) => {
    const stepNumber = index + 1;

    // Check if this step already exists in automation data
    const existingStep = existingStepMap.get(expectedStepName);

    if (existingStep) {
      // Preserve existing step but update step number
      newSteps.push({
        ...existingStep,
        step: stepNumber,
        stepName: expectedStepName // Ensure stepName is current
      });
    } else {
      // Create new step with default automation settings
      newSteps.push({
        step: stepNumber,
        stepName: expectedStepName,
        target: '',
        // value: expectedStepName,
        showInteractionDropdown: false,
        showAssertionDropdown: false,
        // action: '',
        // prompt: expectedStepName
      });
    }
  });

  return newSteps;
};

const handleSubmit = async (formData: any) => {
  try {
    loading.value = true;
    error.value = '';

    if (editingTestCase.value) {
      // Update the test case details
      await axios.patch(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/${editingTestCase.value.id}`,
        formData
      );

      // If the test case has automation data, update it to sync with the new test case steps
      if (editingTestCase.value.automationByAgentq && editingTestCase.value.tcId) {
        try {
          // First, check if automation data exists for this test case
          const automationResponse = await axios.get(
            `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${editingTestCase.value.tcId}/automation`
          );

          // If automation data exists, intelligently merge changes while preserving existing configurations
          if (automationResponse.data && automationResponse.data.steps) {
            // Merge the updated test case steps with existing automation data
            const updatedAutomationSteps = mergeAutomationSteps(formData, automationResponse.data.steps);

            await axios.patch(
              `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/tcId/${editingTestCase.value.tcId}/automation`,
              {
                testCaseId: editingTestCase.value.id,
                steps: updatedAutomationSteps
              }
            );
          }
        } catch (automationErr) {
          // If automation data doesn't exist or there's an error, just log it
          // Don't fail the entire test case update
          console.warn('Failed to update automation data:', automationErr);
        }
      }
    } else {
      await axios.post(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases`,
        formData
      );
    }

    await fetchTestCases();
    showCreateModal.value = false;
    editingTestCase.value = null;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to save test case';
  } finally {
    loading.value = false;
  }
};

const handleExport = async () => {
  try {
    loading.value = true;
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/export`,
      { params: { format: 'csv' }, responseType: 'blob' }
    );
    const url = window.URL.createObjectURL(new Blob([response.data]));
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `test-cases-${projectId}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (err: any) {
    console.error('Failed to export test cases:', err);
    error.value = 'Failed to export test cases';
  } finally {
    loading.value = false;
  }
}

const handleImport = async (file: File) => {
  try {
    if (!file) {
      console.error('No file selected for import.');
      return;
    }

    isImporting.value = true;
    importProgress.value = 0;
    importProgressMessage.value = 'Uploading file...';
    importError.value = null;

    const formData = new FormData();
    formData.append('file', file, file.name);

    // Step 1: Upload the file and start the import process
    const uploadResponse = await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/import/start`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: (progressEvent) => {
          if (progressEvent.total) {
            importProgress.value = Math.round(
              (progressEvent.loaded * 30) / progressEvent.total
            );
          }
        }
      }
    );

    // Get the import job ID from the response
    const importJobId = uploadResponse.data.jobId;

    if (!importJobId) {
      throw new Error('No import job ID received from server');
    }

    importProgressMessage.value = 'Processing file...';
    importProgress.value = 30; // Start processing at 30%

    // Step 2: Poll for import progress
    let isComplete = false;
    const INITIAL_PROGRESS = 30; // Starting progress after file upload
    const PROGRESS_SCALE_FACTOR = 0.65; // Scale server progress to 30-95% range
    let lastProgressUpdate = Date.now();

    while (!isComplete) {
      try {
        // Wait 2 seconds between polls
        await new Promise(resolve => setTimeout(resolve, 2000));

        // Check import status
        const statusResponse = await axios.get(
          `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/import/status/${importJobId}`
        );

        const { status, progress, message, error } = statusResponse.data;

        // Update progress based on server response
        if (progress !== undefined) {
          // Scale progress from 30-95%
          importProgress.value = INITIAL_PROGRESS + Math.round(progress * PROGRESS_SCALE_FACTOR);
          lastProgressUpdate = Date.now();
        }

        // Update message if provided
        if (message) {
          importProgressMessage.value = message;
        }

        // Check for completion or error
        if (status === 'completed') {
          isComplete = true;
          importProgress.value = 100;
          importProgressMessage.value = 'Import completed!';
        } else if (status === 'error') {
          throw new Error(error || 'Import failed on the server');
        }

        // Safety check - if no progress for 2 minutes, consider it an error
        if (Date.now() - lastProgressUpdate > 120000) {
          throw new Error('Import stalled - no progress for 2 minutes');
        }
      } catch (pollError: any) {
        // If polling fails, but not because of a server-reported error,
        // we'll continue polling (server might be busy)
        console.warn('Error polling import status:', pollError);

        // If we get a 404 or 500, the job might have failed
        if (pollError.response && (pollError.response.status === 404 || pollError.response.status === 500)) {
          throw new Error('Import job not found or server error');
        }
      }
    }

    // Import completed successfully
    importProgress.value = 100;
    importProgressMessage.value = 'Import completed!';

    // Give the server a moment to finalize the import
    await new Promise(resolve => setTimeout(resolve, 1000));

    // Force refresh folders first, then test cases
    await fetchFolders();
    await fetchTestCases();

    // Trigger parent component refresh
    emit('refresh');

    // Close modal after successful import
    showImportModal.value = false;

  } catch (error: any) {
    console.error('Error during file import:', error);
    importError.value = error.response?.data ? JSON.stringify(error.response.data) : error.message || 'Failed to import test cases.';
  } finally {
    isImporting.value = false;
  }
};

const closeModal = () => {
  showImportModal.value = false;
  showCreateModal.value = false;
  showViewModal.value = false;
  importError.value = null;
};

onMounted(() => {
  fetchTestCases();
  fetchFolders();
});
</script>

<template>
  <div class="test-cases">
    <div class="test-cases-container">
      <!-- Left sidebar with folder tree -->
      <div class="folder-sidebar">
        <FolderTree
          :key="folders.length"
          :selected-id="selectedFolderId"
          :folders="folders"
          @select-item="handleFolderSelect"
          @structure-updated="fetchFolders"
        />
      </div>

      <!-- Main content -->
      <div class="main-content">
        <div class="header">
          <div class="title-section">
            <!-- <h2>Test Cases</h2> -->
          </div>
          <div class="actions">
            <div class="search-box">
              <input
                type="text"
                v-model="searchQuery"
                placeholder="Search test cases..."
                @keyup.enter="handleSearch"
              />
            </div>
            <button class="show-all-button" @click="
              showingAllTestCases ? showAllTestCases() : clearFilter()
            ">
              <span v-html="filterButtonText"></span>
              <span v-if="selectedFolderName" class="close-icon">×</span>
            </button>
            <button
              @click="openFilterModal"
              class="filter-button"
              :class="{ active: getActiveFilterCount() > 0 }"
            >
              <span class="button-icon">🔍</span> Filter
              <span v-if="getActiveFilterCount() > 0" class="filter-badge">{{ getActiveFilterCount() }}</span>
            </button>
            <button
              v-if="selectedTestCases.length > 0"
              class="delete-button"
              @click="handleBulkDelete"
            >
              Delete Selected
            </button>
            <button
              v-if="selectedTestCases.length > 0"
              class="duplicate-button"
              @click="handleDuplicateTestCases"
            >
              Duplicate Selected
            </button>
            <button
              v-if="selectedTestCases.length > 0"
              class="move-button"
              @click="showMoveFolderModal = true"
            >
              Move to Folder
            </button>
            <button class="icon-button import-button" @click="showImportModal = true" title="Import from CSV">
              <span class="material-icons">📥</span>
            </button>
            <button class="icon-button export-button" @click="showExportModal = true" title="Export to CSV">
              <span class="material-icons">📤</span>
            </button>
            <button class="create-button" @click="handleOpenCreateModal">
              Create Test Case
            </button>

            <button class="action-button generate-button" @click="showGenerateModal = true">
              Generate with AI
            </button>
          </div>
        </div>



        <!-- Filter Modal Component -->
        <FilterTestCasesModal
          v-model="showFilterModal"
          :priority-options="priorityOptions"
          :platform-options="platformOptions"
          :test-case-type-options="testCaseTypeOptions"
          :type-options="typeOptions"
          :tag-options="tagOptions"
          :initial-filters="tempFilters"
          @apply="handleApplyFilters"
        />

        <!-- Active Filters Component -->
        <ActiveTestCaseFilters
          :filters="filters"
          :tag-options="tagOptions"
          @clear="resetAndApplyFilters"
        />

        <!-- Test Cases Table -->
        <TestCasesTable
          :test-cases="filteredTestCases"
          :loading="loading"
          :selected-folder-name="selectedFolderName"
          :selected-test-cases="selectedTestCases"
          :total-items="totalItems"
          :current-page="currentPage"
          :items-per-page="itemsPerPage"
          :sort-field="sortField"
          :sort-direction="sortDirection"
          @update:selected-test-cases="selectedTestCases = $event"
          @edit="handleEditTestCase"
          @delete="handleDeleteTestCase"
          @view="handleViewTestCase"
          @page-change="handlePageChange"
          @sort="handleSort"
        />
      </div>
    </div>

    <!-- Create/Edit Modal -->
    <TestCaseModal
      :key="modalKey"
      :show="showCreateModal"
      :editing-test-case="showViewModal ? viewTestCase : editingTestCase"
      :folders="folders"
      :loading="loading"
      :view-only="showViewModal"
      @close="showCreateModal = false; showViewModal = false;"
      @submit="handleSubmit"
      @refresh="fetchTestCases"
    />

    <!-- AI Generate Modal -->
    <AIGenerateTestCase
      :show="showGenerateModal"
      @close="showGenerateModal = false"
      @generate="handleGenerateTestCases"
    />

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      :is-open="showDeleteModal"
      title="Delete Test Case"
      :message="`Are you sure you want to delete ${selectedTestCases.length > 1 ? 'these test cases' : 'this test case'}? This action cannot be undone.`"
      :is-processing="isDeleting"
      :progress-value="deleteProgress"
      :progress-message="deleteProgressMessage"
      @confirm="handleDeleteConfirm"
      @cancel="() => {
        showDeleteModal = false;
        deletingTestCase = null;
      }"
    />

    <ImportTestCaseModal
      v-if="showImportModal"
      :show="showImportModal"
      :is-importing="isImporting"
      :import-progress="importProgress"
      :import-progress-message="importProgressMessage"
      :error="importError ?? undefined"
      @close="closeModal"
      @import="handleImport"
      @refresh="fetchFolders"
    />

    <ExportTestCaseModal
      v-if="showExportModal"
      @close="showExportModal = false"
      @export="handleExport"
    />

    <MoveFolderModal
      v-if="showMoveFolderModal"
      :show="showMoveFolderModal"
      :folders="folders"
      :selected-count="selectedTestCases.length"
      :loading="isMoving"
      @close="showMoveFolderModal = false"
      @move="handleMoveTestCases"
    />
  </div>
</template>

<style lang="scss" scoped>
.test-cases {
  display: flex;
  flex-direction: column;
}

.test-cases-container {
  display: flex;
  gap: 24px;
  height: 100%;
}

.folder-sidebar {
  width: 220px;
  flex-shrink: 0;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.title-section {
  display: flex;
  align-items: baseline;
  gap: 12px;

  h2 {
    margin: 0;
    font-size: 24px;
    color: #374151;
  }

  .test-count {
    color: #6b7280;
    font-size: 14px;
  }
}

.actions {
  display: flex;
  gap: 12px;

  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    position: relative;
  }

  .folder-name {
    display: inline-block;
  }

  .close-icon {
    position: absolute;
    top: 0px;
    right: 2px;
    font-size: 16px;
    color: #6b7280;
    cursor: pointer;
  }

  .show-all-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;

    &:hover {
      background-color: #e5e7eb;
    }
  }

  .move-button {
    padding: 8px 16px;
    background-color: #8b5cf6;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: #7c3aed;
    }
  }

  .search-box {
    display: flex;
    gap: 8px;

    input {
      width: 300px;
      padding: 8px 12px;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      font-size: 14px;

      &:focus {
        outline: none;
        border-color: #e94560;
        box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
      }
    }
  }

  .delete-button {
    padding: 8px 16px;
    background-color: #ef4444;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: #dc2626;
    }
  }

  .duplicate-button {
    padding: 8px 16px;
    background-color: #3b82f6;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: #2563eb;
    }
  }

  .create-button {
    padding: 8px 16px;
    background-color: #0aa957;
    color: white;
    border: none;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: #056c37;
    }
  }

  .generate-button {
    background-color: #3b82f6;
    color: white;
    border: none;

    &:hover {
      background-color: #2563eb;
    }
  }

  .icon-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    border: none;
    background-color: #f3f4f6;
    cursor: pointer;
    font-size: 24px;
    color: #374151;
    border-radius: 6px;

    &:hover {
      background-color: #c6c7c9;
    }

    &.import-button,
    &.export-button {
      padding: 8px;
    }
  }

  .material-icons {
    font-size: inherit;
    color: inherit;
  }

  .filter-button {
    display: flex;
    align-items: center;
    gap: 6px;
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    position: relative;

    &:hover {
      background-color: #e5e7eb;
    }

    &.active {
      background-color: #fef2f2;
      border-color: #e94560;
      color: #e94560;
    }

    .button-icon {
      font-size: 14px;
    }

    .filter-badge {
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #e94560;
      color: white;
      border-radius: 50%;
      width: 20px;
      height: 20px;
      font-size: 12px;
      font-weight: 600;
      position: absolute;
      top: -8px;
      right: -8px;
    }
  }
}
</style>
