<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';

interface TreeItemProps {
  item: {
    id: string;
    name: string;
    type: 'folder';
    children?: any[];
  };
  level: number;
  selectedId?: string;
}

const props = defineProps<TreeItemProps>();

const emit = defineEmits<{
  'create-folder': [parentId: string];
  'delete-item': [item: TreeItemProps['item']];
  'drag-over': [item: TreeItemProps['item']];
  'drop': [item: TreeItemProps['item']];
  'move-item': [itemId: string, itemType: 'folder', targetFolderId: string | null]; // Add this line
  'select-item': [item: TreeItemProps['item']];
  'drag-start': [item: TreeItemProps['item']];
}>();

const isOpen = ref(false);
const showDropdown = ref(false);

const isSelected = computed(() => props.selectedId === props.item.id);

const toggleFolder = () => {
  isOpen.value = !isOpen.value;
  emit('select-item', props.item);
};

const toggleDropdown = (event: Event) => {
  event.stopPropagation();
  showDropdown.value = !showDropdown.value;
};

const createSubfolder = () => {
  showDropdown.value = false;
  emit('create-folder', props.item.id);
};

const moveItem = (targetFolderId: string | null) => {
  showDropdown.value = false;
  emit('move-item', props.item.id, 'folder', targetFolderId);
};

const deleteItem = () => {
  showDropdown.value = false;
  emit('delete-item', props.item);
};

const isDragOver = ref(false);

const onDragStart = (event: any) => {
  emit('drag-start', props.item); // Notify parent
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', props.item.id);
    event.dataTransfer.effectAllowed = 'move';
  }
};

const onDragOver = (event: DragEvent) => {
  event.preventDefault();
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'move';
  }
};

const onDrop = (event: DragEvent) => {
  event.preventDefault();
  const draggedId = event.dataTransfer?.getData('text/plain');
  if (draggedId && draggedId !== props.item.id) {
    emit('move-item', draggedId, 'folder', props.item.id);
  }
};

</script>

<template>
  <div
  class="tree-item"
    :class="{
      'is-folder': true,
      'is-selected': isSelected,
      'drag-over': isDragOver
    }"
    draggable="true"
    @dragstart="onDragStart"
    @dragover.prevent="onDragOver"
    @dragleave="isDragOver = false"
    @drop.prevent="onDrop"
  >
    <div class="item-content">
      <div class="item-header" @click="toggleFolder">
        <span class="folder-icon">{{ isOpen ? '📂' : '📁' }}</span>
        <span class="item-name">{{ item.name }}</span>
      </div>

      <div class="item-actions">
        <div class="dropdown">
          <button class="dropdown-toggle" @click="toggleDropdown">⋮</button>
          <div v-if="showDropdown" class="dropdown-menu">
            <button @click="createSubfolder">New Folder</button>
            <button @click="moveItem(null)">Move to Root</button>
            <button @click="deleteItem">Delete Folder</button>
          </div>
        </div>
      </div>
    </div>

    <div v-if="isOpen" class="item-children">
      <TreeItem
        v-for="child in item.children"
        :key="child.id"
        :item="child"
        :level="level + 1"
        :selected-id="selectedId"
        @create-folder="$emit('create-folder', $event)"
        @delete-item="$emit('delete-item', $event)"
        @move-item="(itemId, itemType, targetFolderId) => $emit('move-item', itemId, itemType, targetFolderId)"
        @select-item="$emit('select-item', $event)"
        @drag-start="onDragStart"
        @drag-over="$emit('drag-over', $event)"
        @drop="$emit('drop', $event)"
      />
    </div>
  </div>
</template>
   
<style lang="scss" scoped>
  .tree-item {
    margin: 4px 0;

    &.dragging {
    opacity: 0.5;
  }
  }
  
  .item-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
  
    &:hover {
      background-color: #f3f4f6;
    }

    &.drag-over {
    background-color: #e0f2fe;
  }
  }
  
  .item-header {
    display: flex;
    align-items: center;
    gap: 8px;
    flex: 1;
  }
  
  .folder-icon {
    font-size: 16px;
  }
  
  .item-name {
    font-size: 14px;
    color: #374151;
  }
  
  .is-selected > .item-content {
    background-color: #e94560;
    color: white;
  
    .item-name {
      color: white;
    }
  }
  
  .item-actions {
    opacity: 0;
    transition: opacity 0.2s;
  
    .item-content:hover & {
      opacity: 1;
    }
  }
  
  .dropdown {
    position: relative;
  }
  
  .dropdown-toggle {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    padding: 4px 8px;
    font-size: 16px;
  
    &:hover {
      color: #374151;
    }
  }
  
  .dropdown-menu {
    position: absolute;
    right: 0;
    top: 100%;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    z-index: 10;
  
    button {
      display: block;
      width: 100%;
      text-align: left;
      padding: 8px 16px;
      border: none;
      background: none;
      font-size: 14px;
      color: #374151;
      cursor: pointer;
  
      &:hover {
        background-color: #f3f4f6;
      }
  
      &:first-child {
        border-top-left-radius: 6px;
        border-top-right-radius: 6px;
      }
  
      &:last-child {
        border-bottom-left-radius: 6px;
        border-bottom-right-radius: 6px;
      }
    }
  }
  
  .item-children {
    margin-left: 16px;
  }
</style>