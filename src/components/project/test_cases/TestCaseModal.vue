<script setup lang="ts">
import { computed, ref, watch, onMounted, onUnmounted } from 'vue';
import { useRouter } from 'vue-router';
import axios from 'axios';
import { TestCase, Tag, Folder } from '../../../types';

const router = useRouter();

const props = defineProps<{
  show: boolean;
  editingTestCase: TestCase | null;
  folders: Folder[];
  loading: boolean;
  viewOnly: boolean;
}>();

const emit = defineEmits<{
  'close': [];
  'refresh': [];
  'submit': [formData: {
    title: string;
    type: string;
    priority: string;
    platform: string;
    testCaseType: string;
    precondition: string;
    steps: string;
    expectation: string;
    folderId: string;
    tags: string[];
    automationByAgentq: boolean;
  }];
}>();

const createForm = () => ({
  title: '',
  type: '',
  priority: 'medium',
  platform: 'web',
  testCaseType: 'manual',
  precondition: '',
  steps: '',
  expectation: '',
  folderId: '',
  tags: [] as Tag[],
  automationByAgentq: false
});

const form = ref(createForm());
const newTag = ref('');
const availableTags = ref<Tag[]>([]);
const filteredTags = ref<Tag[]>([]);
const loadingTags = ref(false);
const tagError = ref<string | null>(null);
const showTagSuggestions = ref(false);

const fetchTags = async () => {
  try {
    loadingTags.value = true;
    const response = await axios.get(`${(import.meta as any).env.VITE_BACKEND_URL}/tags`);
    availableTags.value = response.data;
  } catch (error) {
    console.error('Failed to fetch tags:', error);
    tagError.value = 'Failed to load tags';
  } finally {
    loadingTags.value = false;
  }
};

const createTag = async () => {
  if (!newTag.value.trim()) return;

  try {
    loadingTags.value = true;
    const response = await axios.post(`${(import.meta as any).env.VITE_BACKEND_URL}/tags`, {
      name: newTag.value.trim()
    });
    
    const createdTag = response.data;
    availableTags.value.push(createdTag);
    form.value.tags.push(createdTag);
    newTag.value = '';
    tagError.value = null;
    showTagSuggestions.value = false;
  } catch (error: any) {
    if (error.response?.status === 409) {
      // Tag already exists, find it and add it
      const existingTag = availableTags.value.find(t => t.name.toLowerCase() === newTag.value.trim().toLowerCase());
      if (existingTag && !form.value.tags.some(t => t.id === existingTag.id)) {
        form.value.tags.push(existingTag);
        newTag.value = '';
        showTagSuggestions.value = false;
      } else {
        tagError.value = 'Tag already added';
      }
    } else {
      tagError.value = 'Failed to create tag';
    }
  } finally {
    loadingTags.value = false;
  }
};

const handleTagInput = () => {
  if (newTag.value.trim() === '') {
    filteredTags.value = [];
    showTagSuggestions.value = false;
    return;
  }
  
  // Filter available tags that:
  // 1. Match the search term
  // 2. Are not already selected
  const searchTerm = newTag.value.toLowerCase();
  filteredTags.value = availableTags.value.filter(tag => 
    tag.name.toLowerCase().includes(searchTerm) && 
    !form.value.tags.some(t => t.id === tag.id)
  );
  
  showTagSuggestions.value = filteredTags.value.length > 0;
};

const handleTagKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault();
    
    // If we have suggestions and one is highlighted, select it
    if (showTagSuggestions.value && filteredTags.value.length > 0) {
      const highlightedTag = filteredTags.value[0]; // You could enhance this with arrow key navigation
      addExistingTag(highlightedTag);
    } else if (newTag.value.trim()) {
      // Otherwise create a new tag
      createTag();
    }
  } else if (event.key === 'Escape') {
    showTagSuggestions.value = false;
  }
};

const addExistingTag = (tag: Tag) => {
  if (!form.value.tags.some(t => t.id === tag.id)) {
    form.value.tags.push(tag);
    tagError.value = null;
    newTag.value = '';
    showTagSuggestions.value = false;
  }
};

const removeTag = (tagToRemove: Tag) => {
  form.value.tags = form.value.tags.filter(tag => tag.id !== tagToRemove.id);
};

// Close tag suggestions when clicking outside
const tagInputRef = ref<HTMLElement | null>(null);
const handleClickOutside = (event: MouseEvent) => {
  if (tagInputRef.value && !tagInputRef.value.contains(event.target as Node)) {
    showTagSuggestions.value = false;
  }
};

// Computed flattened folders for dropdown
const flattenedFolders = computed(() => {
  const flattened: Folder[] = [];
  const flatten = (items: Folder[], level = 0) => {
    items.forEach(item => {
      flattened.push({
        ...item,
        name: '  '.repeat(level) + item.name
      });
      if (item.children) {
        flatten(item.children, level + 1);
      }
    });
  };
  flatten(props.folders);
  return flattened;
});

const resetForm = () => {
  form.value = createForm();
  tagError.value = null;
  newTag.value = '';
};

const closeModal = () => {
  emit('close');
  emit('refresh');
  tagError.value = null;
  newTag.value = '';
};

const handleEsc = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    closeModal();
  }
};

onMounted(() => {
  document.addEventListener('keydown', handleEsc);
  document.addEventListener('mousedown', handleClickOutside);
  fetchTags();
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleEsc);
  document.removeEventListener('mousedown', handleClickOutside);
});

watch(
  () => props.editingTestCase,
  (newValue) => {
    if (newValue) {
      form.value = {
        title: newValue.title,
        type: newValue.type,
        priority: newValue.priority,
        platform: newValue.platform,
        testCaseType: newValue.testCaseType,
        precondition: newValue.precondition,
        steps: newValue.steps,
        expectation: newValue.expectation,
        folderId: newValue.folderId || '',
        tags: newValue.tags || [],
        automationByAgentq: newValue.automationByAgentq || false
      };
    } else if (!props.viewOnly) {
      resetForm();
    }
  },
  { immediate: true }
);

watch(
  () => props.show,
  (newValue) => {
    if (newValue && !props.editingTestCase) {
      resetForm();
    }
  }
);

watch(
  () => newTag.value,
  () => {
    handleTagInput();
  }
);

const handleSubmit = () => {
  // Convert tags array to array of tag IDs before submitting
  const formData = {
    ...form.value,
    tags: form.value.tags.map(tag => tag.id)
  };
  emit('submit', formData);
};

// State for automation options modal
const showAutomationModal = ref(false);

// State for type mismatch popup
const showTypeMismatchModal = ref(false);
const typeMismatchInfo = ref({
  selectedType: '',
  currentType: '',
  message: ''
});

const automateTestCase = () => {
  // Show the automation options modal instead of directly navigating
  showAutomationModal.value = true;
};

const closeAutomationModal = () => {
  showAutomationModal.value = false;
};

const showTypeMismatchPopup = (selectedType: string, currentType: string) => {
  const selectedTypeDisplay = selectedType === 'functional' ? 'Functional Testing' : 'Security Testing (DAST)';
  const currentTypeDisplay = currentType.charAt(0).toUpperCase() + currentType.slice(1);

  typeMismatchInfo.value = {
    selectedType: selectedTypeDisplay,
    currentType: currentTypeDisplay,
    message: `You cannot select "${selectedTypeDisplay}" because this test case type is "${currentTypeDisplay}". Please change the test case type to "${selectedType}" first.`
  };

  showTypeMismatchModal.value = true;
};

const closeTypeMismatchModal = () => {
  showTypeMismatchModal.value = false;
};

const selectAutomationType = (type: 'functional' | 'security - DAST') => {
  if (props.editingTestCase) {
    // Check if test case type matches the selected automation type
    const testCaseType = props.editingTestCase.type?.toLowerCase();

    if (type === 'functional') {
      // Check if test case type is functional
      if (testCaseType !== 'functional') {
        showTypeMismatchPopup('functional', testCaseType || 'unknown');
        return;
      }

      router.push({
        name: 'test-automation',
        params: {
          id: props.editingTestCase.projectId,
          tcId: props.editingTestCase.tcId.toString()
        }
      });
    } else if (type === 'security - DAST') {
      // Check if test case type is security
      if (testCaseType !== 'security - dast') {
        showTypeMismatchPopup('security - DAST', testCaseType || 'unknown');
        return;
      }

      router.push({
        name: 'test-automation-security',
        params: {
          id: props.editingTestCase.projectId,
          tcId: props.editingTestCase.tcId.toString()
        }
      });
    }
    emit('close');
    showAutomationModal.value = false;
  }
};
</script>

<template>
  <div v-if="show" class="modal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>{{ editingTestCase ? (viewOnly ? 'View' : 'Edit') : 'Create' }} Test Case</h3>
        <div class="modal-actions-top">
          <button 
            v-if="editingTestCase"
            class="automate-button"
            @click="automateTestCase"
          >
            Automate by AgentQ
          </button>
          <button class="close-button" @click="closeModal">×</button>
        </div>
      </div>
      <form v-if="!viewOnly" @submit.prevent="handleSubmit">
        <div class="form-group">
          <label>Title</label>
          <input 
            v-model="form.title"
            type="text"
            required
            placeholder="Enter test case title"
          />
        </div>

        <div class="form-group">
          <label>Folder</label>
          <select v-model="form.folderId" required>
            <option 
              v-for="folder in flattenedFolders" 
              :key="folder.id" 
              :value="folder.id"
            >
              {{ folder.name }}
            </option>
          </select>
        </div>

        <div class="form-group">
          <label>Tags</label>
          <div class="tags-input" ref="tagInputRef">
            <div class="tags-container">
              <div class="tag" v-for="tag in form.tags" :key="tag.id">
                {{ tag.name }}
                <button type="button" class="remove-tag" @click="removeTag(tag)">×</button>
              </div>
              <input 
                type="text" 
                v-model="newTag" 
                @keydown="handleTagKeydown"
                placeholder="Add tags..." 
                class="tag-input"
              />
            </div>
            <div v-if="tagError" class="tag-error">{{ tagError }}</div>
            
            <!-- Tag suggestions dropdown -->
            <div v-if="showTagSuggestions" class="tag-suggestions">
              <div 
                v-for="tag in filteredTags" 
                :key="tag.id" 
                @click="addExistingTag(tag)"
                class="tag-suggestion"
              >
                {{ tag.name }}
              </div>
            </div>
          </div>
        </div>

        <div class="form-group">
          <label>Type</label>
          <input 
            v-model="form.type"
            type="text"
            required
            placeholder="e.g., Functional, Integration"
          />
        </div>

        <div class="form-group">
          <label>Priority</label>
          <select v-model="form.priority" required>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="critical">Critical</option>
          </select>
        </div>

        <div class="form-group">
          <label>Platform</label>
          <select v-model="form.platform" required>
            <option value="web">Web</option>
            <option value="mobile">Mobile</option>
            <option value="api">API</option>
            <option value="desktop">Desktop</option>
          </select>
        </div>

        <div class="form-group">
          <label>Test Case Type</label>
          <select v-model="form.testCaseType">
            <option value="manual">Manual</option>
            <option value="automation">Automation</option>
          </select>
        </div>

        <div class="form-group">
          <label>Precondition</label>
          <textarea
            v-model="form.precondition"
            rows="3"
            placeholder="Enter test case precondition"
          ></textarea>
        </div>

        <div class="form-group">
          <label>Steps</label>
          <textarea
            v-model="form.steps"
            rows="5"
            required
            placeholder="Enter test steps"
          ></textarea>
        </div>

        <div class="form-group">
          <label>Expected Result</label>
          <textarea
            v-model="form.expectation"
            rows="3"
            required
            placeholder="Enter expected result"
          ></textarea>
        </div>

        <div class="modal-actions">
          <button 
            type="button" 
            class="cancel-button"
            @click="closeModal"
          >
            Cancel
          </button>
          <button 
            type="submit" 
            class="submit-button"
            :disabled="loading"
          >
            {{ editingTestCase ? 'Update' : 'Create' }}
          </button>
        </div>
      </form>
      <div v-else class="view-form">
        <div class="form-group">
          <label>Title</label>
          <div class="view-field">{{ form.title }}</div>
        </div>
        <div class="form-group">
          <label>Folder</label>
          <div class="view-field">{{ flattenedFolders.find(f => f.id === form.folderId)?.name }}</div>
        </div>
        <div class="form-group">
          <label>Tags</label>
          <div class="view-field tags-display">
            <span v-for="tag in form.tags" :key="tag.id" class="tag">
              {{ tag.name }}
            </span>
          </div>
        </div>
        <div class="form-group">
          <label>Type</label>
          <div class="view-field">{{ form.type }}</div>
        </div>
        <div class="form-group">
          <label>Priority</label>
          <div class="view-field">{{ form.priority }}</div>
        </div>
        <div class="form-group">
          <label>Platform</label>
          <div class="view-field">{{ form.platform }}</div>
        </div>
        <div class="form-group">
          <label>Test Case Type</label>
          <div class="view-field">{{ form.testCaseType }}</div>
        </div>
        <div class="form-group">
          <label>Precondition</label>
          <div class="view-field view-textarea">{{ form.precondition }}</div>
        </div>
        <div class="form-group">
          <label>Steps</label>
          <div class="view-field view-textarea">{{ form.steps }}</div>
        </div>
        <div class="form-group">
          <label>Expected Result</label>
          <div class="view-field view-textarea">{{ form.expectation }}</div>
        </div>
        <div class="modal-actions">
          <button 
            type="button" 
            class="cancel-button"
            @click="emit('close')"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Automation Options Modal -->
  <div v-if="showAutomationModal" class="modal automation-options-modal">
    <div class="modal-content automation-options-content">
      <div class="modal-header">
        <h3>Choose Automation Type</h3>
        <button class="close-button" @click="closeAutomationModal">×</button>
      </div>

      <div class="automation-options">
        <div
          class="option-card"
          :class="{ 'disabled': props.editingTestCase?.type?.toLowerCase() !== 'functional' }"
          @click="selectAutomationType('functional')"
        >
          <div class="option-icon">
            <img src="/icons/functional-test.svg" alt="Functional Testing" class="option-image" />
          </div>
          <h4>Functional Testing</h4>
          <p>Automate functional test scenarios to verify application behavior and user workflows</p>
          <div class="option-button">
            <span class="icon">🔧</span>
            {{ props.editingTestCase?.type?.toLowerCase() === 'functional' ? 'Select' : 'Type Mismatch' }}
          </div>
          <div v-if="props.editingTestCase?.type?.toLowerCase() !== 'functional'" class="mismatch-indicator">
            ⚠️ Requires "Functional" type
          </div>
        </div>

        <div
          class="option-card"
          :class="{ 'disabled': props.editingTestCase?.type?.toLowerCase() !== 'security - dast' }"
          @click="selectAutomationType('security - DAST')"
        >
          <div class="option-icon">
            <img src="/icons/security-test.svg" alt="Security Testing" class="option-image" />
          </div>
          <h4>Security Testing (DAST) - BETA*</h4>
          <p>Perform dynamic application security testing to identify vulnerabilities and security issues</p>
          <div class="option-button">
            <span class="icon">🔒</span>
            {{ props.editingTestCase?.type?.toLowerCase() === 'security - dast' ? 'Select' : 'Type Mismatch' }}
          </div>
          <div v-if="props.editingTestCase?.type?.toLowerCase() !== 'security - dast'" class="mismatch-indicator">
            ⚠️ Requires "Security - DAST" type
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Type Mismatch Information Modal -->
  <div v-if="showTypeMismatchModal" class="modal type-mismatch-modal">
    <div class="modal-content type-mismatch-content">
      <div class="modal-header">
        <h3>⚠️ Test Case Type Mismatch</h3>
        <button class="close-button" @click="closeTypeMismatchModal">×</button>
      </div>

      <div class="mismatch-info">
        <div class="mismatch-message">
          <p>{{ typeMismatchInfo.message }}</p>
        </div>

        <div class="type-comparison">
          <div class="type-item current">
            <div class="type-label">Current Test Case Type:</div>
            <div class="type-value">{{ typeMismatchInfo.currentType }}</div>
          </div>
          <div class="type-arrow">→</div>
          <div class="type-item required">
            <div class="type-label">Required for {{ typeMismatchInfo.selectedType }}:</div>
            <div class="type-value">{{ typeMismatchInfo.selectedType === 'Functional Testing' ? 'Functional' : 'Security - DAST' }}</div>
          </div>
        </div>

        <div class="recommendation">
          <h4>💡 Recommendation:</h4>
          <p>
            1. Close this dialog<br>
            2. Edit the test case and change the "Type" field to "{{ typeMismatchInfo.selectedType === 'Functional Testing' ? 'Functional' : 'Security - DAST' }}"<br>
            3. Save the changes<br>
            4. Try selecting {{ typeMismatchInfo.selectedType }} again
          </p>
        </div>
      </div>

      <div class="modal-actions">
        <button class="primary-button" @click="closeTypeMismatchModal">
          <span class="icon">✓</span>
          Got it
        </button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 600px;
  max-height: 90vh;
  overflow-y: auto;

  h3 {
    margin: 0 0 24px;
    font-size: 20px;
    color: #374151;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  
  h3 {
    margin: 0;
  }
}

.modal-actions-top {
  display: flex;
  align-items: center;
  gap: 12px;
}

.automate-button {
  background-color: #4285f4;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  
  &:hover {
    background-color: #3367d6;
  }
}

.form-group {
  margin-bottom: 16px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
  }

  input, select, textarea, .view-field {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }

  .view-field {
    background-color: #f9fafb;
    min-height: 38px;
  }

  .view-textarea {
    min-height: 80px;
    white-space: pre-line;
  }

  textarea {
    resize: vertical;
  }
}

.view-form {
  .view-field {
    &:empty:before {
      content: 'Not specified';
      color: #9ca3af;
      font-style: italic;
    }
  }
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;

  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
  }

  .cancel-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;

    &:hover {
      background-color: #e5e7eb;
    }
  }

  .submit-button {
    background-color: #e94560;
    color: white;
    border: none;

    &:hover:not(:disabled) {
      background-color: #d63553;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;

  &:hover {
    color: #374151;
  }
}

.tags-input {
  display: flex;
  flex-direction: column;
  gap: 8px;
  position: relative;

  .tags-container {
    display: flex;
    flex-wrap: wrap;
    gap: 4px;
    min-height: 38px;
    padding: 4px 8px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    background-color: #fff;
    align-items: center;
  }

  .tag-input {
    border: none;
    padding: 4px;
    margin: 0;
    flex-grow: 1;
    min-width: 80px;
    background: transparent;
    
    &:focus {
      outline: none;
      box-shadow: none;
    }
  }

  .tag-error {
    color: #ef4444;
    font-size: 12px;
  }
}

.tag {
  background-color: #f3f4f6;
  color: #374151;
  border-radius: 16px;
  padding: 4px 8px;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;

  .remove-tag {
    background: none;
    border: none;
    color: #6b7280;
    cursor: pointer;
    font-size: 14px;
    padding: 0;

    &:hover {
      color: #374151;
    }
  }
}

.tag-suggestions {
  position: absolute;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  z-index: 10;
  top: 100%;
  margin-top: 4px;
}

.tag-suggestion {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background-color: #f3f4f6;
  }
}

.tags-display {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  padding: 8px !important;

  .tag {
    margin: 0;
  }
}

// Automation Options Modal Styles
.automation-options-modal {
  z-index: 1100; // Higher than the main modal
}

.automation-options-content {
  max-width: 800px;
  width: 95%;
}

.automation-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-top: 24px;
}

.option-card {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;

  &:hover {
    border-color: #e94560;
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(233, 69, 96, 0.15);
    background: linear-gradient(135deg, #fff 0%, #f8fafc 100%);
  }

  &:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #e94560, #4285f4);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover:before {
    opacity: 1;
  }
}

.option-icon {
  margin-bottom: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  height: 80px;
}

.option-image {
  width: 64px;
  height: 64px;
  object-fit: contain;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.option-card h4 {
  margin: 0 0 12px;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.option-card p {
  margin: 0 0 20px;
  font-size: 14px;
  color: #64748b;
  line-height: 1.5;
}

.option-button {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background-color: #e94560;
  color: white;
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
  font-size: 14px;
  transition: all 0.3s ease;

  .icon {
    font-size: 16px;
  }
}

.option-card:hover .option-button {
  background-color: #d63553;
  transform: scale(1.05);
}

.option-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f8f9fa;
  border-color: #dee2e6;
}

.option-card.disabled:hover {
  transform: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.option-card.disabled .option-button {
  background: #6c757d;
  color: white;
}

.option-card.disabled:hover .option-button {
  background: #6c757d;
  transform: none;
}

.mismatch-indicator {
  margin-top: 10px;
  padding: 8px 12px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 6px;
  color: #856404;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
}

@media (max-width: 768px) {
  .automation-options {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .automation-options-content {
    width: 90%;
    padding: 16px;
  }

  .option-card {
    padding: 20px;
  }
}

/* Type Mismatch Modal Styles */
.type-mismatch-modal {
  z-index: 1200;
}

.type-mismatch-content {
  max-width: 600px;
  width: 90%;
}

.mismatch-info {
  padding: 20px 0;
}

.mismatch-message {
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 20px;
  color: #856404;
}

.mismatch-message p {
  margin: 0;
  font-weight: 500;
}

.type-comparison {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.type-item {
  text-align: center;
  flex: 1;
}

.type-label {
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
  font-weight: 500;
}

.type-value {
  font-size: 16px;
  font-weight: 600;
  padding: 8px 12px;
  border-radius: 6px;
}

.type-item.current .type-value {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.type-item.required .type-value {
  background: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.type-arrow {
  font-size: 24px;
  color: #007bff;
  font-weight: bold;
}

.recommendation {
  background: #e7f3ff;
  border: 1px solid #b3d9ff;
  border-radius: 8px;
  padding: 15px;
  margin-top: 20px;
}

.recommendation h4 {
  margin: 0 0 10px 0;
  color: #0056b3;
  font-size: 14px;
}

.recommendation p {
  margin: 0;
  color: #0056b3;
  line-height: 1.5;
  font-size: 14px;
}

.primary-button {
  background: #007bff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: background 0.2s ease;
}

.primary-button:hover {
  background: #0056b3;
}

.primary-button .icon {
  font-size: 16px;
}
</style>
