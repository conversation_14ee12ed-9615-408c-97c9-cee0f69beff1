<script setup lang="ts">
import { ref, onMounted } from 'vue';
import axios from 'axios';

interface ApiKey {
  id: string;
  provider: string;
  apiKey: string;
}

interface UploadedFile {
  id: string;
  filename: string;
  mimetype: string;
  status: string;
  createdAt: string;
}

interface PublishedUrl {
  id: string;
  url_name: string;
  url: string;
  url_type: string;
  integration?: string;
  status: string;
  created_at: string;
}

interface ProcessedItem {
  id: string;
  type: 'file' | 'url';
}

const props = defineProps<{
  processedItems: ProcessedItem[];
}>();

const emit = defineEmits<{
  'stepBack': [];
  'nextStep': [];
}>();

const uploadedFiles = ref<UploadedFile[]>([]);
const publishedUrls = ref<PublishedUrl[]>([]);
const loading = ref(false);
const error = ref('');
const success = ref('');
const generating = ref(false);
const generationProgress = ref(0);
const generationStatus = ref('');
const apiKeys = ref<ApiKey[]>([]);

const getFileType = (mimetype: string): string => {
  const typeMap: Record<string, string> = {
    'application/pdf': 'PDF',
    'text/markdown': 'Markdown',
    'text/plain': 'Text',
    'text/html': 'HTML',
    'application/msword': 'Word',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word',
    'application/vnd.ms-excel': 'Excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',
    'text/csv': 'CSV'
  };
  return typeMap[mimetype] || 'Unknown';
};

const fetchApiKeys = async () => {
  try {
    const response = await axios.get(`${(import.meta as any).env.VITE_BACKEND_URL}/api-keys`);
    apiKeys.value = response.data;
  } catch (err) {
    console.error('Failed to fetch API keys:', err);
  }
};

const fetchData = async () => {
  try {
    loading.value = true;
    const dataPromises = props.processedItems.map(item => {
      const endpoint = item.type === 'url'
        ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${item.id}`
        : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${item.id}`;
      return axios.get(endpoint);
    });
    
    const responses = await Promise.all(dataPromises);
    
    // Reset arrays
    uploadedFiles.value = [];
    publishedUrls.value = [];
    
    // Process responses based on their type
    responses.forEach((response, index) => {
      const item = props.processedItems[index];
      if (item.type === 'url') {
        publishedUrls.value.push(response.data);
      } else {
        uploadedFiles.value.push(response.data);
      }
    });
  } catch (err) {
    console.error('Failed to fetch data:', err);
    error.value = 'Failed to fetch data';
  } finally {
    loading.value = false;
  }
};

const deleteItem = async (item: UploadedFile | PublishedUrl, type: 'file' | 'url') => {
  try {
    loading.value = true;
    error.value = '';
    success.value = '';

    const endpoint = type === 'url'
      ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${item.id}`
      : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${item.id}`;

    await axios.delete(endpoint);
    success.value = `${type === 'url' ? 'URL' : 'File'} deleted successfully`;
    
    // Remove the deleted item from the appropriate list
    if (type === 'url') {
      publishedUrls.value = publishedUrls.value.filter(url => url.id !== item.id);
    } else {
      uploadedFiles.value = uploadedFiles.value.filter(file => file.id !== item.id);
    }
    
    // If no items left, step back
    if (uploadedFiles.value.length === 0 && publishedUrls.value.length === 0) {
      setTimeout(() => {
        emit('stepBack');
      }, 1500);
    }
  } catch (err) {
    console.error('Failed to delete:', err);
    error.value = `Failed to delete ${type === 'url' ? 'URL' : 'file'}`;
  } finally {
    loading.value = false;
  }
};

const generateTestCases = async () => {
  try {
    generating.value = true;
    error.value = '';
    success.value = '';
    generationProgress.value = 0;
    
    const allItems = [
      ...uploadedFiles.value.map(file => ({ item: file, type: 'file' as const })),
      ...publishedUrls.value.map(url => ({ item: url, type: 'url' as const }))
    ];
    
    const totalItems = allItems.length;
    let completedItems = 0;
    
    for (const { item, type } of allItems) {
      try {
        generationStatus.value = `Generating test cases for ${type === 'file' ? item.filename : item.url} (${completedItems + 1}/${totalItems})`;
        
        const endpoint = type === 'url'
          ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${item.id}/ai-generate-test-cases`
          : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${item.id}/ai-generate-test-cases`;
        
        const generateResponse = await axios.post(endpoint);

        if (generateResponse.status === 201) {
          const usageEndpoint = type === 'url'
            ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${item.id}/completion-usage`
            : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${item.id}/completion-usage`;
          
          const completionUsageResponse = await axios.get(usageEndpoint);

          if (completionUsageResponse.status === 200) {
            await axios.post(`${(import.meta as any).env.VITE_CORE_SERVICE_URL}/profile/usage-token`, {
              tokenType: completionUsageResponse.data.usageByType[0].tokenType,
              tokenUsed: completionUsageResponse.data.totalTokensUsed,
              token: apiKeys.value[0].apiKey
            });
          }
        }

        completedItems++;
        generationProgress.value = (completedItems / totalItems) * 100;
      } catch (err) {
        console.error(`Failed to generate test cases for ${type} ${item.id}:`, err);
        error.value = 'Failed to generate test cases for one or more items. Please try again.';
        return;
      }
    }

    if (completedItems === totalItems) {
      generationStatus.value = 'All test cases generated successfully!';
      setTimeout(() => {
        emit('nextStep');
      }, 1000);
    }
  } catch (err) {
    console.error('Failed to generate test cases:', err);
    error.value = 'Failed to generate test cases. Please try again.';
  } finally {
    generating.value = false;
    generationStatus.value = '';
  }
};

onMounted(() => {
  if (props.processedItems.length > 0) {
    fetchData();
    fetchApiKeys();
  }
});
</script>

<template>
  <div class="data-processing">
    <h2 class="title">Review Data Preparation</h2>
    
    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <div v-if="success" class="success-message">
      {{ success }}
    </div>
    
    <div class="table-container">
      <!-- Files Table -->
      <div v-if="uploadedFiles.length > 0" class="section">
        <h3 class="section-title">Uploaded Files</h3>
        <table class="data-table">
          <thead>
            <tr>
              <th>File Name</th>
              <th>Type</th>
              <th>Upload Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="file in uploadedFiles" :key="file.id">
              <td>{{ file.filename }}</td>
              <td>
                <span class="type-badge file">{{ getFileType(file.mimetype) }}</span>
              </td>
              <td>{{ new Date(file.createdAt).toLocaleDateString() }}</td>
              <td>
                <button 
                  class="delete-button"
                  @click="deleteItem(file, 'file')"
                  :disabled="loading || generating"
                >
                  🗑️ Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- URLs Table -->
      <div v-if="publishedUrls.length > 0" class="section">
        <h3 class="section-title">Processed URLs</h3>
        <table class="data-table">
          <thead>
            <tr>
              <th>Type</th>
              <th>URL</th>
              <th>Created Date</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="url in publishedUrls" :key="url.id">
              <td>
                <span :class="['type-badge', 'url', url.url_type]">
                  {{ url.url_type }}
                </span>
              </td>
              <td>
                <a :href="url.url" target="_blank" rel="noopener noreferrer" class="url-link">
                  {{ url.url.length > 30 ? url.url.substring(0, 30) + '...' : url.url }}
                </a>
              </td>
              <td>{{ new Date(url.created_at).toLocaleDateString() }}</td>
              <td>
                <button 
                  class="delete-button"
                  @click="deleteItem(url, 'url')"
                  :disabled="loading || generating"
                >
                  🗑️ Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Empty State -->
      <div v-if="!loading && uploadedFiles.length === 0 && publishedUrls.length === 0" class="empty-state">
        No files or URLs have been processed yet.
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="loading-overlay">
        <div class="loading-spinner"></div>
      </div>
    </div>

    <!-- Generation Progress -->
    <div v-if="generating" class="generation-progress">
      <div class="progress-bar">
        <div 
          class="progress-fill"
          :style="{ width: `${generationProgress}%` }"
        ></div>
      </div>
      <div class="progress-status">{{ generationStatus }}</div>
      <div class="progress-percentage">{{ Math.round(generationProgress) }}%</div>
    </div>

    <div class="actions">
      <button 
        class="generate-button"
        @click="generateTestCases"
        :disabled="loading || generating || (!uploadedFiles.length && !publishedUrls.length)"
      >
        {{ generating ? 'Generating...' : 'Generate Test Cases' }}
      </button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.data-processing {
  .title {
    font-size: 18px;
    color: #374151;
    margin-bottom: 24px;
    font-weight: 500;
  }
}

.section {
  margin-bottom: 32px;

  .section-title {
    font-size: 16px;
    font-weight: 500;
    color: #374151;
    margin-bottom: 16px;
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.success-message {
  background-color: #dcfce7;
  color: #166534;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.table-container {
  margin-top: 24px;
  position: relative;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  
  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }
  
  th {
    background-color: #f3f4f6;
    font-weight: 500;
    color: #374151;
  }
  
  td {
    color: #6b7280;
  }
}

.type-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;

  &.file {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.url {
    &.public {
      background-color: #dcfce7;
      color: #166534;
    }

    &.private {
      background-color: #fef3c7;
      color: #92400e;
    }
  }
}

.url-link {
  color: #2563eb;
  text-decoration: none;
  
  &:hover {
    text-decoration: underline;
  }
}

.delete-button {
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
  color: #dc2626;
  background-color: #fef2f2;
  border: 1px solid #fee2e2;
  cursor: pointer;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background-color: #fee2e2;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #e94560;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.empty-state {
  text-align: center;
  padding: 48px;
  background-color: #f9fafb;
  border-radius: 8px;
  color: #6b7280;
}

.generation-progress {
  margin: 24px 0;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;

  .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
  }

  .progress-fill {
    height: 100%;
    background-color: #e94560;
    transition: width 0.3s ease;
  }

  .progress-status {
    text-align: center;
    color: #374151;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .progress-percentage {
    text-align: center;
    color: #6b7280;
    font-size: 12px;
  }
}

.actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;

  .generate-button {
    padding: 12px 24px;
    background-color: #e94560;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover:not(:disabled) {
      background-color: #d63553;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>