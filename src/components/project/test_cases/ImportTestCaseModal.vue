<script setup lang="ts">
import { ref, defineEmits, defineProps, onMounted, onBeforeUnmount, onUnmounted } from 'vue';

const props = defineProps({
  isImporting: Boolean,
  importProgress: Number,
  importProgressMessage: String,
  error: String
});

const { isImporting, importProgress, importProgressMessage, error } = props;
const emit = defineEmits(['close', 'import', 'refresh']);
const file = ref<File | null>(null);
const fileInput = ref<HTMLInputElement | null>(null);
const isDragging = ref(false);

const handleFileChange = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files && input.files.length > 0) {
    file.value = input.files[0];
  }
};

const downloadCsvTemplate = () => {
  const csvContent = `Title,Type,Platform,Priority,Test Case Type,Precondition,Steps,Expected Results,Folder,Tags\ntitle test case,functional/security/performance/etc,web/mobile/api,low/medium/high/critical,manual/automation,explain the precondition,explain the steps,explain the expectation,folder/subfolder/sub subfolder,"tag1,tag2,tag3"`;
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement("a");
  const url = URL.createObjectURL(blob);
  link.setAttribute("href", url);
  link.setAttribute("download", "test_case_template.csv");
  link.style.visibility = 'hidden';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

const handleImport = () => {
  if (!file.value) {
    console.error('No file selected for import.');
    return;
  }
  emit('import', file.value);
  emit('refresh');
  // Don't close immediately - let parent handle it after import completes
};

const preventDefault = (e: Event) => {
  e.preventDefault();
  e.stopPropagation();
};

const handleDrop = (e: DragEvent) => {
  preventDefault(e);
  isDragging.value = false;
  
  if (e.dataTransfer?.files && e.dataTransfer.files.length > 0) {
    const droppedFile = e.dataTransfer.files[0];
    
    // Validate file type
    if (!droppedFile.name.endsWith('.csv')) {
      alert('Please upload a CSV file only');
      return;
    }
    
    file.value = droppedFile;
    
    // Create a new DataTransfer to update the file input
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(droppedFile);
    if (fileInput.value) {
      fileInput.value.files = dataTransfer.files;
    }
  }
};

const handleDragOver = (e: Event) => {
  preventDefault(e);
  isDragging.value = true;
};

const handleDragEnter = (e: Event) => {
  preventDefault(e);
  isDragging.value = true;
};

const handleDragLeave = (e: Event) => {
  preventDefault(e);
  // Only set to false if we're leaving the drop zone
  const relatedTarget = (e as DragEvent).relatedTarget as HTMLElement;
  if (!relatedTarget || !(e.currentTarget as HTMLElement).contains(relatedTarget)) {
    isDragging.value = false;
  }
};

const handleEsc = (event: KeyboardEvent) => {
  if (event.key === 'Escape') {
    emit('close');
    emit('refresh');
  }
};

onUnmounted(() => {
  document.removeEventListener('keydown', handleEsc);
});

onMounted(() => {
  document.addEventListener('keydown', handleEsc);
  const dropZone = document.querySelector('.file-upload');
  if (dropZone) {
    dropZone.addEventListener('dragover', handleDragOver);
    dropZone.addEventListener('drop', handleDrop as EventListener);
    dropZone.addEventListener('dragenter', handleDragEnter);
    dropZone.addEventListener('dragleave', handleDragLeave);
  }
});

onBeforeUnmount(() => {
  const dropZone = document.querySelector('.file-upload');
  if (dropZone) {
    dropZone.removeEventListener('dragover', handleDragOver);
    dropZone.removeEventListener('drop', handleDrop as EventListener);
    dropZone.removeEventListener('dragenter', handleDragEnter);
    dropZone.removeEventListener('dragleave', handleDragLeave);
  }
});
</script>

<template>
  <div class="modal">
    <div class="modal-content">
      <button class="close-button" @click="emit('close')">×</button>
      <h2>Import Test Cases</h2>
      <div class="mt-4">
        <p>
          Download <a @click="downloadCsvTemplate" class="csv-template-link">CSV template</a> for import reference.
        </p>
      </div>
      <div class="mt-4">
        <p>Supported format: CSV only</p>
      </div>

      <div v-if="!isImporting">
        <div class="file-upload" :class="{ 'drag-active': isDragging }">
          <div v-if="!file" class="file-upload-placeholder">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="file-upload-icon">
              <path fill-rule="evenodd" d="M18.97 3.63a1.5 1.5 0 0 1 2.1 2.1L12 14.7 4.93 7.63a1.5 1.5 0 0 1 2.1-2.1L12 12.6l7.97-8.97ZM3 17.25a.75.75 0 0 1 .75-.75h16.5a.75.75 0 0 1 0 1.5H3.75A.75.75 0 0 1 3 17.25Z" clip-rule="evenodd" />
            </svg>
            <p>Drag and drop a CSV file here or <label for="file-input" class="file-upload-button-label">click to select</label></p>
          </div>
          <div v-else class="file-upload-preview">
            {{ file.name }}
          </div>
          <input
            type="file"
            ref="fileInput"
            id="file-input"
            @change="handleFileChange"
            accept=".csv"
            required
          />
        </div>

        <div class="form-actions">
          <button class="primary-button" @click.stop="handleImport" :disabled="!file">
            Import
          </button>
          <button class="cancel-button" @click="emit('close')">
            Cancel
          </button>
        </div>
      </div>

      <div v-else class="import-progress">
        <p>{{ importProgressMessage }}</p>
        <progress 
          :value="importProgress" 
          max="100"
          class="progress-bar"
        ></progress>
        <span>{{ importProgress }}%</span>
      </div>

      <p v-if="error" class="error">{{ error }}</p>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  padding: 24px;
  border-radius: 12px;
  width: 500px;
  max-width: 90%;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  overflow: hidden;
  position: relative;
}

.close-button {
  position: absolute;
  top: 12px;
  right: 12px;
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6b7280;
  opacity: 0.7;

  &:hover {
    opacity: 1;
  }
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 24px;
}

.primary-button {
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: #d63553;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.cancel-button {
  background-color: #f3f4f6;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover:not(:disabled) {
    background-color: #e5e7eb;
  }
}

.file-upload {
  border: 2px dashed #d1d5db;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.2s;

  &:hover {
    border-color: #9ca3af;
  }
}

.file-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.file-upload-icon {
  width: 32px;
  height: 32px;
  color: #9ca3af;
}

.file-upload-button-label {
  color: #e94560;
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: #d63553;
  }
}

.file-upload-preview {
  padding: 12px;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  color: #374151;
  text-align: left;
  overflow-x: auto;
}

#file-input {
  display: none;
}

.file-upload.drag-active {
  border-color: #e94560;
  background-color: #fdf2f4;
}

.import-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  padding: 20px;
  text-align: center;
  
  p {
    margin: 0;
    font-size: 16px;
    color: #374151;
  }
  
  span {
    font-size: 14px;
    color: #6b7280;
  }
}

.progress-bar {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  
  &::-webkit-progress-bar {
    background-color: #f3f4f6;
    border-radius: 4px;
  }
  
  &::-webkit-progress-value {
    background-color: #e94560;
    border-radius: 4px;
    transition: width 0.3s ease;
  }
  
  &::-moz-progress-bar {
    background-color: #e94560;
    border-radius: 4px;
  }
}

.error {
  color: #ef4444;
  margin-top: 16px;
  text-align: center;
  font-size: 14px;
}

.csv-template-link {
  color: #e94560;
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: #d63553;
  }
}
</style>