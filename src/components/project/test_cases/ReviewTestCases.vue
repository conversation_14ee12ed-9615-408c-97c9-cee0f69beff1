<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import ConfirmationModal from '../../common/ConfirmationModal.vue';

interface TestCase {
  id: string;
  name: string;
  precondition: string;
  steps: string[];
  expectation: string;
  testType: string;
  priority: string;
  platform: string;
  testCaseType: string;
  automationByAgentq: boolean;
  sourceId?: string;
  sourceType?: 'file' | 'url';
}

interface ProcessedItem {
  id: string;
  type: 'file' | 'url';
}

const props = defineProps<{
  processedItems: ProcessedItem[];
}>();

const emit = defineEmits<{
  'next-step': [];
  'step-back': [];
}>();

const route = useRoute();
const projectId = route.params.id as string;

const testCases = ref<TestCase[]>([]);
const loading = ref(false);
const error = ref('');
const success = ref('');
const processingTestCases = ref(false);
const editingId = ref<string | null>(null);
const fieldErrors = ref<{[key: string]: string[]}>({});

// Modal states
const showDeleteModal = ref(false);
const deletingTestCase = ref<TestCase | null>(null);

// Temporary edit state
const editingTestCase = ref<TestCase | null>(null);

// Valid platform values
const validPlatforms = ['web', 'mobile', 'api', 'desktop'];

// Function to check if platform is valid
const isPlatformValid = (platform: string): boolean => {
  return validPlatforms.includes(platform?.toLowerCase());
};

// Pagination
const currentPage = ref(1);
const itemsPerPage = ref(10);

// New refs for generate more functionality
const isGeneratingMore = ref(false);
const generateMoreProgress = ref(0);
const generateMoreStatus = ref('');

const paginatedTestCases = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return testCases.value.slice(start, end);
});

const totalPages = computed(() => Math.ceil(testCases.value.length / itemsPerPage.value));

const handlePageChange = (page: number) => {
  currentPage.value = page;
};

const generateMoreTestCases = async () => {
  try {
    isGeneratingMore.value = true;
    generateMoreProgress.value = 0;
    generateMoreStatus.value = 'Generating additional test cases...';
    error.value = '';

    for (const item of props.processedItems) {
      try {
        const endpoint = item.type === 'url'
          ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${item.id}/ai-generate-more-test-cases`
          : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${item.id}/ai-generate-more-test-cases`;

        const response = await axios.post(endpoint);

        // Add source information to each test case
        const newTestCases = response.data.map((tc: TestCase) => ({
          ...tc,
          sourceId: item.id,
          sourceType: item.type
        }));

        // Append new test cases to existing ones
        testCases.value = [...testCases.value, ...newTestCases];

        // Update progress
        generateMoreProgress.value += (100 / props.processedItems.length);
      } catch (err) {
        console.error(`Failed to generate more test cases for ${item.type} ${item.id}:`, err);
        throw err;
      }
    }

    success.value = 'Additional test cases generated successfully';
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to generate additional test cases';
  } finally {
    isGeneratingMore.value = false;
    generateMoreStatus.value = '';
    generateMoreProgress.value = 0;
  }
};

const fetchGeneratedTestCases = async () => {
  try {
    loading.value = true;
    error.value = '';
    const allTestCases: TestCase[] = [];

    // Fetch test cases for each processed item
    for (const item of props.processedItems) {
      try {
        const endpoint = item.type === 'url'
          ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${item.id}/ai-generate-test-cases`
          : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${item.id}/ai-generate-test-cases`;

        const response = await axios.get(endpoint);

        // Add source information to each test case
        const testCasesWithSource = response.data.map((tc: TestCase) => ({
          ...tc,
          sourceId: item.id,
          sourceType: item.type
        }));

        allTestCases.push(...testCasesWithSource);
      } catch (err) {
        console.error(`Failed to fetch test cases for ${item.type} ${item.id}:`, err);
      }
    }

    if (allTestCases.length === 0) {
      error.value = 'No test cases found for any of the processed items';
      setTimeout(() => {
        emit('step-back');
      }, 500);
      return;
    }

    testCases.value = allTestCases;
  } catch (err: any) {
    console.error('Failed to fetch generated test cases:', err);
    error.value = err.response?.data?.message || 'Failed to fetch test cases';
  } finally {
    loading.value = false;
  }
};

const startEdit = (testCase: TestCase) => {
  editingId.value = testCase.id;
  editingTestCase.value = JSON.parse(JSON.stringify(testCase));
};

const cancelEdit = () => {
  editingId.value = null;
  editingTestCase.value = null;
};

const saveEdit = async () => {
  if (!editingTestCase.value) return;

  try {
    loading.value = true;
    error.value = '';

    const endpoint = editingTestCase.value.sourceType === 'url'
      ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${editingTestCase.value.sourceId}/ai-generate-test-cases/${editingTestCase.value.id}`
      : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${editingTestCase.value.sourceId}/ai-generate-test-cases/${editingTestCase.value.id}`;

    await axios.patch(endpoint, editingTestCase.value);
    await fetchGeneratedTestCases();
    editingId.value = null;
    editingTestCase.value = null;
    success.value = 'Test case updated successfully';
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to update test case';
  } finally {
    loading.value = false;
  }
};

const confirmDelete = (testCase: TestCase) => {
  deletingTestCase.value = testCase;
  showDeleteModal.value = true;
};

const handleDeleteConfirm = async () => {
  if (!deletingTestCase.value) return;

  try {
    loading.value = true;
    error.value = '';

    const endpoint = deletingTestCase.value.sourceType === 'url'
      ? `${(import.meta as any).env.VITE_AI_SERVICE_URL}/publish_url/${deletingTestCase.value.sourceId}/ai-generate-test-cases/${deletingTestCase.value.id}`
      : `${(import.meta as any).env.VITE_AI_SERVICE_URL}/file-upload/${deletingTestCase.value.sourceId}/ai-generate-test-cases/${deletingTestCase.value.id}`;

    await axios.delete(endpoint);
    await fetchGeneratedTestCases();
    showDeleteModal.value = false;
    deletingTestCase.value = null;
    success.value = 'Test case deleted successfully';
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to delete test case';
  } finally {
    loading.value = false;
  }
};

const handleReviewComplete = async () => {
  try {
    processingTestCases.value = true;
    error.value = '';
    fieldErrors.value = {};

    // Pre-validate platforms before sending to server
    const invalidPlatforms = testCases.value.filter(tc => !isPlatformValid(tc.platform));
    if (invalidPlatforms.length > 0) {
      fieldErrors.value['platform'] = invalidPlatforms.map(tc => tc.id);
      error.value = 'Platform must be one of the following values: web, mobile, api, desktop';
      throw new Error('Invalid platform values');
    }

    // Process all test cases
    for (const testCase of testCases.value) {
      try {
        await axios.post(`${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases`, {
          title: testCase.name,
          precondition: testCase.precondition,
          steps: testCase.steps.join('\n'),
          expectation: testCase.expectation,
          priority: testCase.priority?.toLowerCase() || 'medium',
          type: testCase.testType,
          platform: testCase.platform?.toLowerCase() || 'web',
          testCaseType: testCase.testCaseType?.toLowerCase() || 'manual',
          automationByAgentq: testCase.automationByAgentq
        });
      } catch (err: any) {
        console.error('Failed to process test case:', testCase.name, err);

        // Check for platform validation error
        if (err.response?.data?.message?.includes('platform must be one of the following values')) {
          fieldErrors.value['platform'] = testCases.value.map(tc => tc.id);
          error.value = 'Platform must be one of the following values: web, mobile, api, desktop';
        } else {
          error.value = err.response?.data?.message || 'Failed to process test cases';
        }

        throw err;
      }
    }

    emit('next-step');
  } catch (err: any) {
    if (!error.value) {
      error.value = err.response?.data?.message || 'Failed to process test cases';
    }
  } finally {
    processingTestCases.value = false;
  }
};

onMounted(() => {
  if (props.processedItems.length > 0) {
    fetchGeneratedTestCases();
  }
});
</script>

<template>
  <div class="review-test-cases">
    <h2 class="title">Review Generated Test Cases</h2>

    <div v-if="error" class="error-message">
      {{ error }}
    </div>

    <div v-if="success" class="success-message">
      {{ success }}
    </div>

    <!-- Test Cases Table -->
    <div class="table-container">
      <table class="test-cases-table">
        <thead>
          <tr>
            <th>No.</th>
            <th>Name</th>
            <th>Precondition</th>
            <th>Steps</th>
            <th>Expected Result</th>
            <th>Test Type</th>
            <th>Priority</th>
            <th>Platform</th>
            <th>Actions</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(testCase, index) in paginatedTestCases" :key="testCase.id">
            <!-- Editing Mode -->
            <template v-if="editingId === testCase.id && editingTestCase">
              <td>{{ (currentPage - 1) * itemsPerPage + index + 1 }}</td>
              <td>
                <input
                  v-model="editingTestCase.name"
                  class="edit-input"
                  @keyup.enter="saveEdit"
                  @keyup.esc="cancelEdit"
                />
              </td>
              <td>
                <textarea
                  v-model="editingTestCase.precondition"
                  class="edit-input"
                  rows="3"
                  @keyup.enter="saveEdit"
                  @keyup.esc="cancelEdit"
                ></textarea>
              </td>
              <td>
                <div v-for="(step, index) in editingTestCase.steps" :key="index" class="step-edit">
                  <input
                    v-model="editingTestCase.steps[index]"
                    class="edit-input"
                    @keyup.enter="saveEdit"
                    @keyup.esc="cancelEdit"
                  />
                  <span>{{ step }}</span>
                  <button class="remove-step" @click="editingTestCase.steps.splice(index, 1)">×</button>
                </div>
                <button class="add-step" @click="editingTestCase.steps.push('')">+ Add Step</button>
              </td>
              <td>
                <textarea
                  v-model="editingTestCase.expectation"
                  class="edit-input"
                  rows="3"
                  @keyup.enter="saveEdit"
                  @keyup.esc="cancelEdit"
                ></textarea>
              </td>
              <td>
                <input
                  v-model="editingTestCase.testType"
                  class="edit-input"
                  @keyup.enter="saveEdit"
                  @keyup.esc="cancelEdit"
                />
              </td>
              <td>
                <select v-model="editingTestCase.priority" class="edit-input">
                  <option value="Low">Low</option>
                  <option value="Medium">Medium</option>
                  <option value="High">High</option>
                  <option value="Critical">Critical</option>
                </select>
              </td>
              <td>
                <input
                  v-model="editingTestCase.platform"
                  class="edit-input"
                  :class="{
                    'error-input': fieldErrors['platform']?.includes(editingTestCase.id) ||
                                  (editingTestCase.platform && !isPlatformValid(editingTestCase.platform))
                  }"
                  @keyup.enter="saveEdit"
                  @keyup.esc="cancelEdit"
                />
                <div v-if="fieldErrors['platform']?.includes(editingTestCase.id) ||
                          (editingTestCase.platform && !isPlatformValid(editingTestCase.platform))"
                     class="field-error-hint">
                  Must be one of: web, mobile, api, desktop
                </div>
              </td>
              <td class="actions">
                <button class="action-button save" @click="saveEdit">
                  💾
                </button>
                <button class="action-button cancel" @click="cancelEdit">
                  ❌
                </button>
              </td>
            </template>

            <!-- View Mode -->
            <template v-else>
              <td>{{ (currentPage - 1) * itemsPerPage + index + 1 }}</td>
              <td>{{ testCase.name }}</td>
              <td>{{ testCase.precondition }}</td>
              <td>
                <ul class="steps-list">
                  <li v-for="(step, index) in testCase.steps" :key="index">
                    {{ step }}
                  </li>
                </ul>
              </td>
              <td>{{ testCase.expectation }}</td>
              <td>{{ testCase.testType }}</td>
              <td>{{ testCase.priority }}</td>
              <td :class="{ 'error-field': fieldErrors['platform']?.includes(testCase.id) || !isPlatformValid(testCase.platform) }">{{ testCase.platform }}</td>
              <td class="actions">
                <button
                  class="action-button edit"
                  @click="startEdit(testCase)"
                  :disabled="loading"
                >
                  ✏️
                </button>
                <button
                  class="action-button delete"
                  @click="confirmDelete(testCase)"
                  :disabled="loading"
                >
                  🗑️
                </button>
              </td>
            </template>
          </tr>
        </tbody>
      </table>

      <!-- Pagination -->
      <div class="pagination">
        <button
          class="page-button"
          :disabled="currentPage === 1"
          @click="handlePageChange(currentPage - 1)"
        >
          Previous
        </button>
        <span class="page-info">
          Page {{ currentPage }} of {{ totalPages }}
        </span>
        <button
          class="page-button"
          :disabled="currentPage === totalPages"
          @click="handlePageChange(currentPage + 1)"
        >
          Next
        </button>
      </div>

      <!-- Generate More Progress -->
      <div v-if="isGeneratingMore" class="generate-more-progress">
        <div class="progress-bar">
          <div
            class="progress-fill"
            :style="{ width: `${generateMoreProgress}%` }"
          ></div>
        </div>
        <div class="progress-status">{{ generateMoreStatus }}</div>
        <div class="progress-percentage">{{ Math.round(generateMoreProgress) }}%</div>
      </div>

      <!-- Loading State -->
      <div v-if="loading" class="loading-state">
        Loading test cases...
      </div>

      <!-- Empty State -->
      <div v-if="!loading && testCases.length === 0" class="empty-state">
        No test cases found.
      </div>
    </div>

    <!-- Review Actions -->
    <div class="review-actions">
      <button
        class="generate-more-button"
        @click="generateMoreTestCases"
        :disabled="loading || isGeneratingMore || !testCases.length"
      >
        <span v-if="isGeneratingMore">
          <div class="button-spinner"></div>
          Generating...
        </span>
        <span v-else>
          Generate More Test Cases
        </span>
      </button>

      <button
        class="review-button"
        @click="handleReviewComplete"
        :disabled="loading || processingTestCases || !testCases.length"
      >
        <span v-if="processingTestCases">
          <div class="button-spinner"></div>
          Processing...
        </span>
        <span v-else>
          Complete Review
        </span>
      </button>
    </div>

    <!-- Delete Confirmation Modal -->
    <ConfirmationModal
      :is-open="showDeleteModal"
      title="Delete Test Case"
      :message="`Are you sure you want to delete this test case? This action cannot be undone.`"
      @confirm="handleDeleteConfirm"
      @cancel="() => { showDeleteModal = false; deletingTestCase = null; }"
    />
  </div>
</template>

<style lang="scss" scoped>
.review-test-cases {
  .title {
    font-size: 18px;
    color: #374151;
    margin-bottom: 24px;
    font-weight: 500;
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.success-message {
  background-color: #dcfce7;
  color: #166534;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.test-cases-table {
  width: 100%;
  border-collapse: collapse;

  th, td {
    padding: 12px;
    text-align: left;
    border-bottom: none;
    vertical-align: top;
    font-size: 14px;
  }

  th {
    background-color: #f9fafb;
    font-weight: 500;
    color: #374151;
  }

  .steps-list {
    list-style-type: decimal;
    margin: 0;
    padding-left: 20px;
  }

  .edit-input {
    width: 100%;
    padding: 8px;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    font-size: 14px;
    margin-bottom: 4px;

    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }

    &.error-input {
      border-color: #dc2626;
      background-color: #fee2e2;
    }
  }

  .error-field {
    background-color: #fee2e2;
    color: #dc2626;
    font-weight: 500;
  }

  .field-error-hint {
    color: #dc2626;
    font-size: 12px;
    margin-top: 4px;
  }

  .step-edit {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
  }

  .remove-step {
    padding: 0 8px;
    background-color: #fee2e2;
    color: #dc2626;
    border: none;
    border-radius: 4px;
    cursor: pointer;

    &:hover {
      background-color: #fecaca;
    }
  }

  .add-step {
    padding: 8px 16px;
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;

    &:hover {
      background-color: #e5e7eb;
    }
  }

  .actions {
    display: flex;
    gap: 8px;
  }

  .action-button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: 1px solid #e5e7eb;
    background-color: white;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &.edit {
      color: #059669;

      &:hover:not(:disabled) {
        background-color: #f0fdf4;
      }
    }

    &.delete {
      color: #dc2626;

      &:hover:not(:disabled) {
        background-color: #fef2f2;
      }
    }

    &.save {
      color: #059669;
      background-color: #f0fdf4;

      &:hover:not(:disabled) {
        background-color: #dcfce7;
      }
    }

    &.cancel {
      color: #6b7280;

      &:hover:not(:disabled) {
        background-color: #f3f4f6;
      }
    }
  }
}

.loading-state, .empty-state {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-top: 24px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;

  .page-button {
    padding: 8px 16px;
    background-color: white;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    color: #374151;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover:not(:disabled) {
      background-color: #f3f4f6;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .page-info {
    color: #6b7280;
    font-size: 14px;
  }
}

.generate-more-progress {
  margin-top: 24px;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 8px;

  .progress-bar {
    width: 100%;
    height: 8px;
    background-color: #e5e7eb;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 12px;
  }

  .progress-fill {
    height: 100%;
    background-color: #e94560;
    transition: width 0.3s ease;
  }

  .progress-status {
    text-align: center;
    color: #374151;
    font-size: 14px;
    margin-bottom: 4px;
  }

  .progress-percentage {
    text-align: center;
    color: #6b7280;
    font-size: 12px;
  }
}

.review-actions {
  margin-top: 24px;
  display: flex;
  justify-content: center;
  gap: 16px;

  .generate-more-button {
    padding: 12px 24px;
    background-color: #8b5cf6;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;

    &:hover:not(:disabled) {
      background-color: #7c3aed;
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
  }

  .review-button {
    padding: 12px 24px;
    background-color: #e94560;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;

    &:hover:not(:disabled) {
      background-color: #d63553;
    }

    &:disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }

    .button-spinner {
      width: 20px;
      height: 20px;
      border: 3px solid rgba(255, 255, 255, 0.3);
      border-top: 3px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>