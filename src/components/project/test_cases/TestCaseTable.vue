<script setup lang="ts">
import { ref, computed, defineProps, defineEmits } from 'vue';
import { TestCase } from '../../../types/index';

const gotoPage = ref<number | null>(null);

const props = defineProps<{
  testCases: TestCase[];
  loading: boolean;
  selectedFolderName?: string;
  selectedTestCases: string[];
  totalItems: number;
  currentPage: number;
  itemsPerPage: number;
  sortField: string;
  sortDirection: string;
}>();

const emit = defineEmits<{
  'update:selectedTestCases': [value: string[]];
  'view': [testCase: TestCase];
  'edit': [testCase: TestCase];
  'delete': [testCase: TestCase];
  'page-change': [page: number];
  'sort': [field: string];
}>();

const allSelected = computed({
  get: () => props.testCases.length > 0 && props.selectedTestCases.length === props.testCases.length,
  set: (value: boolean) => {
    emit('update:selectedTestCases', value ? props.testCases.map(tr => tr.id) : []);
  }
});

// Pagination
const maxVisiblePages = 10;

const totalPages = computed(() => Math.ceil(props.totalItems / props.itemsPerPage));

const handlePageChange = (page: number) => {
  emit('page-change', page);
};

const handleGotoPage = () => {
  if (gotoPage.value !== null) {
    // Ensure the page number is within valid range
    const pageNum = Math.max(1, Math.min(totalPages.value, gotoPage.value));
    handlePageChange(pageNum);
    gotoPage.value = null; // Clear the input after navigation
  }
};

// const handleEdit = (testCase: TestCase) => {
//   emit('edit', testCase);
// };

// const handleDelete = (testCase: TestCase) => {
//   emit('delete', testCase);
// };

// Handle sorting
const handleSort = (field: string) => {
  emit('sort', field);
};

// Get sort icon based on current sort state
const getSortIcon = (field: string) => {
  if (props.sortField !== field) return '↕️';
  return props.sortDirection === 'asc' ? '↑' : '↓';
};

const visiblePages = computed(() => {
  const pages: (number | string)[] = [];

  if (totalPages.value <= maxVisiblePages) {
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    pages.push(1); // Always show the first page

    if (props.currentPage <= Math.ceil(maxVisiblePages / 2) + 1) {
      // Near the beginning
      for (let i = 2; i <= maxVisiblePages - 2; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages.value);
    } else if (props.currentPage >= totalPages.value - Math.floor(maxVisiblePages / 2)) {
      // Near the end
      pages.push('...');
      for (let i = totalPages.value - maxVisiblePages + 3; i < totalPages.value; i++) {
        pages.push(i);
      }
      pages.push(totalPages.value);
    } else {
      // In the middle
      pages.push('...');
      const start = props.currentPage - Math.floor(maxVisiblePages / 2) + 1;
      const end = props.currentPage + Math.floor(maxVisiblePages / 2) - 1;
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      pages.push('...');
      pages.push(totalPages.value);
    }
  }

  return pages;
});

const toggleSelection = (testRunId: string) => {
  const selected = [...props.selectedTestCases];
  const index = selected.indexOf(testRunId);

  if (index === -1) {
    selected.push(testRunId);
  } else {
    selected.splice(index, 1);
  }

  emit('update:selectedTestCases', selected);
};
</script>

<template>
  <div class="table-container">
    <!-- Table Info -->
    <div class="table-info">
      <div class="info-text">
        <span class="total-count">Total {{ props.totalItems }} test cases</span>
        <span v-if="selectedFolderName" class="folder-name">
          in folder "{{ selectedFolderName }}"
        </span>
        <span class="pagination-info">
          (Showing {{ (props.currentPage - 1) * props.itemsPerPage + 1 }} -
          {{ Math.min(props.currentPage * props.itemsPerPage, props.totalItems) }})
        </span>
      </div>
    </div>

    <!-- Table -->
    <table v-if="props.testCases.length > 0" class="test-cases-table">
      <thead>
        <tr>
          <th class="checkbox-column">
            <input
              type="checkbox"
              :checked="allSelected"
              @change="e => allSelected = (e.target as HTMLInputElement).checked"
            />
          </th>
          <th @click="handleSort('tcId')" class="sortable-header">
            ID <span class="sort-icon">{{ getSortIcon('tcId') }}</span>
          </th>
          <th @click="handleSort('title')" class="sortable-header">
            Title <span class="sort-icon">{{ getSortIcon('title') }}</span>
          </th>
          <th @click="handleSort('type')" class="sortable-header">
            Type <span class="sort-icon">{{ getSortIcon('type') }}</span>
          </th>
          <th @click="handleSort('priority')" class="sortable-header">
            Priority <span class="sort-icon">{{ getSortIcon('priority') }}</span>
          </th>
          <th @click="handleSort('platform')" class="sortable-header">
            Platform <span class="sort-icon">{{ getSortIcon('platform') }}</span>
          </th>
          <th @click="handleSort('testCaseType')" class="sortable-header">
            Test Case Type <span class="sort-icon">{{ getSortIcon('testCaseType') }}</span>
          </th>
          <!-- <th>Tag</th> -->
          <!-- <th>Actions</th> -->
        </tr>
      </thead>
      <tbody>
        <tr v-for="testCase in props.testCases":key="testCase.id" class="test-case-row" @click="$emit('edit', testCase)">
          <td class="checkbox-column" @click.stop>
              <input
                type="checkbox"
                :checked="selectedTestCases.includes(testCase.id)"
                @change="() => toggleSelection(testCase.id)"
              />
          </td>
          <td>TC-{{ testCase.tcId.toString().padStart(0, '0') }}</td>
          <td class="title-column">{{ testCase.title }}</td>
          <td>{{ testCase.type.toLowerCase() }}</td>
          <td>
            <span :class="['priority-badge', testCase.priority]">
              {{ testCase.priority }}
            </span>
          </td>
          <td>{{ testCase.platform }}</td>
          <td>{{ testCase.testCaseType.toLowerCase() === 'manual' ? 'Manual' : (testCase.testCaseType.toLowerCase() === 'automation' ? 'Automation' : testCase.testCaseType) }}</td>
          <!-- <td>
            <template v-if="testCase.tags && testCase.tags.length > 0">
              <template v-if="testCase.tags.length < 2">
                <span v-for="tag in testCase.tags" :key="tag.id" class="tag">
                  {{ tag.name }}
                </span>
              </template>
              <template v-else>
                <span class="tag">...</span>
              </template>
            </template>
            <template v-else>
              <span></span>
            </template>
          </td> -->
          <!-- <td class="actions">
            <button class="action-button edit" @click.stop="handleEdit(testCase)">
              ✏️
            </button>
            <button class="action-button delete" @click.stop="handleDelete(testCase)">
              🗑️
            </button>
          </td> -->
        </tr>
      </tbody>
    </table>

    <div v-else-if="loading" class="loading-state">
      Loading test cases...
    </div>

    <div v-else class="empty-state">
      No test cases found. Create one or generate with AI.
    </div>

    <!-- Pagination -->
    <div v-if="totalPages > 1" class="pagination">
      <button
        class="page-button"
        :disabled="props.currentPage === 1"
        @click="handlePageChange(props.currentPage - 1)"
      >
        Previous
      </button>

      <div class="page-numbers">
        <button
          v-for="page in visiblePages"
          :key="page"
          class="page-number"
          :class="{ active: props.currentPage === page }"
          @click="page !== '...' ? handlePageChange(page as number) : () => {}"
          :disabled="page === '...'"
        >
          {{ page }}
        </button>
      </div>

      <button
        class="page-button"
        :disabled="props.currentPage === totalPages"
        @click="handlePageChange(props.currentPage + 1)"
      >
        Next
      </button>
      <div class="goto-page">
        <input
          type="number"
          v-model="gotoPage"
          min="1"
          :max="totalPages"
          placeholder="Page"
          @keyup.enter="handleGotoPage"
        />
        <button class="goto-button" @click="handleGotoPage">Go</button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.table-container {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.table-info {
  padding: 16px;
  border-bottom: 1px solid #e5e7eb;

  .info-text {
    color: #374151;
    font-size: 14px;

    .total-count {
      font-weight: 600;
    }

    .folder-name {
      margin-left: 8px;
      color: #6b7280;
    }
  }
}

.test-cases-table {
  width: 100%;
  border-collapse: collapse;

  th,
      td {
        padding: 12px;
        text-align: center;
        border-bottom: 1px solid #e5e7eb;
      }

      td.title-column {
        text-align: left;
      }

  th {
    background-color: #f9fafb;
    font-weight: 500;
    color: #374151;
    text-align: center;

    &.sortable-header {
      cursor: pointer;
      user-select: none;
      position: relative;
      padding-right: 24px;

      &:hover {
        background-color: #e2e8f0;
      }

      .sort-icon {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        font-size: 14px;
        color: #64748b;
      }
    }
  }

  .checkbox-column {
    width: 40px;
    text-align: center;

    input[type="checkbox"] {
      cursor: pointer;
    }
  }

  .actions {
      display: flex;
      gap: 8px;
      align-items: center; /* Vertically center items */
      font-size: inherit; /* Reset font-size */
      font-weight: normal; /* Reset font-weight */
    }

  .action-button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: 1px solid #e5e7eb;
    background-color: white;
    box-sizing: border-box;

    &.edit {
      color: #059669;

      &:hover {
        background-color: #f0fdf4;
      }
    }

    &.delete {
      color: #dc2626;

      &:hover {
        background-color: #fef2f2;
      }
    }
  }

  .tag {
  display: inline-block;
  background-color: #f0f0f0; /* Light gray background */
  color: #333; /* Dark text */
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 4px;
  font-size: 0.9em;
}

  .priority-badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
    text-transform: capitalize;

    &.low {
      background-color: #dbeafe;
      color: #1e40af;
    }

    &.medium {
      background-color: #fef3c7;
      color: #92400e;
    }

    &.high {
      background-color: #fee2e2;
      color: #991b1b;
    }

    &.critical {
      background-color: #7f1d1d;
      color: white;
    }
  }
}

.test-case-row {
      cursor: pointer;
      font-size: 14px;

      &:hover {
        background-color: #f9fafb;
      }
    }

.loading-state {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.empty-state {
  text-align: center;
  padding: 40px;
  color: #6b7280;
}

.pagination {
  display: flex;
  align-items: center;
  justify-content: center; /* Center the main content */
  gap: 8px;
  padding: 16px;
  border-top: 1px solid #e5e7eb;
  position: relative; /* Needed for absolute positioning of goto-page */
}

.page-button {
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;

  &:hover:not(:disabled) {
    background-color: #f9fafb;
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background-color: white;
  color: #374151;
  font-size: 14px;
  cursor: pointer;

  &:hover:not(.active) {
    background-color: #f9fafb;
  }

  &.active {
    background-color: #e94560;
    color: white;
    border-color: #e94560;
  }
}

.goto-page {
  display: flex;
  align-items: center;
  position: absolute; /* Position it relative to the pagination container */
  right: 16px; /* Adjust as needed for spacing from the right */
}

.goto-page input {
  width: 80px;
  height: 30px;
  padding: 0 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  text-align: center;
  font-size: 14px;
  font-weight: normal;
}

.goto-page input:focus {
  outline: none; /* Remove the default focus outline */
  border-color: red; /* Set the border color to red on focus */
}

.goto-button {
  height: 30px;
  font-size: 14px;
  font-weight: normal;
  padding: 0 8px;
  margin-left: 5px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
}

.goto-button:hover {
  background-color: #e9e9e9;
}
</style>