<template>
  <div v-if="modelValue" class="modal-overlay">
    <div class="filter-modal">
      <div class="modal-header">
        <h3>Filter Issues</h3>
        <button class="close-button" @click="closeModal">×</button>
      </div>
      
      <div class="modal-body">
        <div class="filter-grid">
          <!-- Status Filter -->
          <div class="filter-group">
            <h4>Status</h4>
            <div class="checkbox-group">
              <div 
                v-for="status in statusOptions" 
                :key="status" 
                class="checkbox-item"
              >
                <label>
                  <input 
                    type="checkbox" 
                    :checked="filters.status.includes(status)"
                    @change="toggleStatusFilter(status)"
                  />
                  <span 
                    class="status-badge small" 
                    :class="'status-' + status.toLowerCase().replace(/\s+/g, '')"
                  >
                    {{ status }}
                  </span>
                </label>
              </div>
            </div>
          </div>
          
          <!-- Test Run Filter -->
          <div class="filter-group">
            <h4>Test Run</h4>
            <div class="checkbox-group">
              <div 
                v-for="testRun in testRunOptions" 
                :key="testRun.id" 
                class="checkbox-item"
              >
                <label>
                  <input 
                    type="checkbox" 
                    :checked="filters.testRunId.includes(testRun.id)"
                    @change="toggleTestRunFilter(testRun.id)"
                  />
                  {{ testRun.name }}
                </label>
              </div>
            </div>
          </div>
          
          <!-- Date Range Filter -->
          <div class="filter-group">
            <h4>Created Date</h4>
            <div class="date-inputs">
              <div class="date-input">
                <label>From:</label>
                <input 
                  type="date" 
                  v-model="filters.createdAfter"
                />
              </div>
              <div class="date-input">
                <label>To:</label>
                <input 
                  type="date" 
                  v-model="filters.createdBefore"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button @click="resetFilters" class="reset-button">Reset</button>
        <button @click="closeModal" class="cancel-button">Cancel</button>
        <button @click="applyFilters" class="apply-button">Apply Filters</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

interface TestRun {
  id: string;
  name: string;
}

const props = defineProps<{
  modelValue: boolean;
  statusOptions: string[];
  testRunOptions: TestRun[];
  initialFilters: {
    status: string[];
    testRunId: string[];
    createdAfter: string;
    createdBefore: string;
  };
}>();

type Filters = {
  status: string[];
  testRunId: string[];
  createdAfter: string;
  createdBefore: string;
};

const emit = defineEmits<{
  (e: 'update:modelValue', value: boolean): void;
  (e: 'apply', filters: Filters): void;
}>();

// Local copy of filters for the modal
const filters = ref({
  status: [...props.initialFilters.status],
  testRunId: [...props.initialFilters.testRunId],
  createdAfter: props.initialFilters.createdAfter,
  createdBefore: props.initialFilters.createdBefore
});

// Watch for changes in initialFilters
watch(() => props.initialFilters, (newFilters) => {
  if (props.modelValue) return; // Don't update while modal is open
  
  filters.value = {
    status: [...newFilters.status],
    testRunId: [...newFilters.testRunId],
    createdAfter: newFilters.createdAfter,
    createdBefore: newFilters.createdBefore
  };
}, { deep: true });

// Close modal
const closeModal = () => {
  emit('update:modelValue', false);
};

// Apply filters
const applyFilters = () => {
  emit('apply', {
    status: [...filters.value.status],
    testRunId: [...filters.value.testRunId],
    createdAfter: filters.value.createdAfter,
    createdBefore: filters.value.createdBefore
  });
  closeModal();
};

// Reset filters
const resetFilters = () => {
  filters.value = {
    status: [],
    testRunId: [],
    createdAfter: '',
    createdBefore: ''
  };
};

// Toggle a status filter
const toggleStatusFilter = (status: string) => {
  const index = filters.value.status.indexOf(status);
  if (index === -1) {
    filters.value.status.push(status);
  } else {
    filters.value.status.splice(index, 1);
  }
};

// Toggle a test run filter
const toggleTestRunFilter = (id: string) => {
  const index = filters.value.testRunId.indexOf(id);
  if (index === -1) {
    filters.value.testRunId.push(id);
  } else {
    filters.value.testRunId.splice(index, 1);
  }
};
</script>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.filter-modal {
  background-color: white;
  border-radius: 8px;
  width: 800px;
  max-width: 90%;
  max-height: 90vh;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  h3 {
    margin: 0;
    font-size: 18px;
    color: #374151;
  }
  
  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    color: #6b7280;
    cursor: pointer;
    
    &:hover {
      color: #374151;
    }
  }
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  max-height: calc(90vh - 130px);
}

.filter-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.filter-group {
  h4 {
    margin: 0 0 8px 0;
    font-size: 14px;
    color: #4b5563;
  }
  
  .checkbox-group {
    max-height: 150px;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
    border-radius: 4px;
    padding: 8px;
    background-color: white;
    
    .checkbox-item {
      margin-bottom: 6px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 14px;
        cursor: pointer;
        
        input[type="checkbox"] {
          cursor: pointer;
        }
      }
    }
  }
  
  .date-inputs {
    display: flex;
    flex-direction: column;
    gap: 8px;
    
    .date-input {
      display: flex;
      flex-direction: column;
      gap: 4px;
      
      label {
        font-size: 12px;
        color: #6b7280;
      }
      
      input[type="date"] {
        padding: 6px 8px;
        border: 1px solid #e5e7eb;
        border-radius: 4px;
        font-size: 14px;
        
        &:focus {
          outline: none;
          border-color: #e94560;
          box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
        }
      }
    }
  }
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  
  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
  }
  
  .reset-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    
    &:hover {
      background-color: #e5e7eb;
    }
  }
  
  .cancel-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;
    
    &:hover {
      background-color: #e5e7eb;
    }
  }
  
  .apply-button {
    background-color: #e94560;
    color: white;
    border: none;
    
    &:hover {
      background-color: #d93a53;
    }
  }
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-transform: capitalize;

  &.small {
    padding: 2px 6px;
    font-size: 11px;
  }

  /* AgentQ statuses */
  &.status-open {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.status-in-progress {
    background-color: #e0f2fe;
    color: #0369a1;
  }

  &.status-resolved {
    background-color: #dcfce7;
    color: #166534;
  }

  &.status-closed {
    background-color: #f3f4f6;
    color: #4b5563;
  }

  &.status-deleted {
    background-color: #fee2e2;
    color: #b91c1c;
  }

  /* JIRA statuses - lowercase and no spaces for CSS class names */
  &.status-todo, &.status-backlog {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.status-inprogress {
    background-color: #e0f2fe;
    color: #0369a1;
  }

  &.status-done, &.status-completed {
    background-color: #dcfce7;
    color: #166534;
  }

  &.status-onhold, &.status-blocked {
    background-color: #fef3c7;
    color: #92400e;
  }

  /* Default for any other status */
  &:not([class*="status-"]) {
    background-color: #f3f4f6;
    color: #4b5563;
  }
}
</style>
