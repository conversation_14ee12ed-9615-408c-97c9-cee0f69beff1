<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';

interface TestRun {
  id: string;
  name: string;
  description: string;
  environment: string | null;
  build: string | null;
  release: string | null;
  selectionType: string;
  selectedTestCaseIds?: string[]; 
  dynamicFilters?: {
    priorities: string[];
    testCaseTypes: string[];
    platforms: string[];
    folders: string[];
    tags: string[];
  };
}

interface TestCase {
  id: string;
  tcId: number;
  title: string;
  type: string;
  priority: string;
  platform: string;
  testCaseType: string;
  folderId: string | null;
  tags: {id: string, name: string}[] | null;
} 

interface Folder {
  id: string;
  name: string;
  children?: Folder[];
  testCases?: TestCase[];
}

interface Tags {
  id: string;
  name: string;
}

const props = defineProps<{
  show: boolean;
  editingTestRun: TestRun | null;
  loading: boolean;
}>();

const emit = defineEmits<{
  'close': [];
  'refresh': [];
  'submit': [formData: any];
}>();

const route = useRoute();
const projectId = route.params.id as string;

// Form data
const name = ref('');
const description = ref('');
const environment = ref('');
const build = ref('');
const release = ref('');

// Test case selection
const activeTab = ref('all');
const folders = ref<Folder[]>([]);
const tags = ref<Tags[]>([]); 
const testCases = ref<TestCase[]>([]);
const selectedTestCases = ref<string[]>([]);

// Filter criteria
const selectedPriorities = ref<string[]>([]);
const selectedPlatforms = ref<string[]>([]);
const selectedTestCaseTypes = ref<string[]>([]);
const selectedFolders = ref<string[]>([]);
const selectedTags = ref<string[]>([]);

const fetchTestCases = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases?all=true`
    );
    testCases.value = response.data;
  } catch (err) {
    console.error('Failed to fetch test cases:', err);
  }
};

const fetchTags = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/tags/projects/${projectId}`
    );
    tags.value = response.data;
  } catch (err) {
    console.error('Failed to fetch test cases:', err);
  }
};

const fetchFolders = async () => {
  try {
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-cases/folders`
    );
    folders.value = flattenFolders(response.data); // Flatten the folder structure
  } catch (err) {
    console.error('Failed to fetch folders:', err);
  }
};

const flattenFolders = (folderList: Folder[], flattened: Folder[] = []) => {
  folderList.forEach((folder) => {
    flattened.push(folder);
    if (folder.children && folder.children.length > 0) {
      flattenFolders(folder.children, flattened);
    }
  });
  return flattened;
};

const closeModal = () => {
  emit('close');
  emit('refresh');
};

const resetForm = () => {
  name.value = '';
  description.value = '';
  environment.value = '';
  build.value = '';
  release.value = '';
  selectedTestCases.value = [];
  selectedPriorities.value = [];
  selectedPlatforms.value = [];
  selectedTestCaseTypes.value = [];
  selectedFolders.value = [];
  selectedTags.value = [];
  activeTab.value = 'all';
};

const handleSubmit = () => {
  let testCaseIdsToSend: string[] = [];
  let dynamicFiltersToSend: {
    priorities?: string[];
    testCaseTypes?: string[];
    platforms?: string[];
    folders?: string[];
    tags?: string[];
  } | null = null;


  if (activeTab.value === 'all') {
    testCaseIdsToSend = testCases.value.map(tc => tc.id);
  } else if (activeTab.value === 'specific') {
    testCaseIdsToSend = selectedTestCases.value;
  } else if (activeTab.value === 'dynamic') {

    dynamicFiltersToSend = {
      priorities: selectedPriorities.value,
      platforms: selectedPlatforms.value,
      testCaseTypes: selectedTestCaseTypes.value,
      folders: selectedFolders.value,
      tags: selectedTags.value
    };

    testCaseIdsToSend = testCases.value
      .filter(tc => {
        const matchesPriority = selectedPriorities.value.length === 0 ||
          selectedPriorities.value.includes(tc.priority);
        const matchesPlatform = selectedPlatforms.value.length === 0 ||
          selectedPlatforms.value.includes(tc.platform);
        const matchesTestCaseType = selectedTestCaseTypes.value.length === 0 ||
          selectedTestCaseTypes.value.includes(tc.testCaseType);
        const matchesFolder = selectedFolders.value.length === 0 ||
          selectedFolders.value.includes(tc.folderId || 'none');
          const matchesTag = selectedTags.value.length === 0 || 
          (selectedTags.value.includes('none') && (!tc.tags || tc.tags.length === 0)) ||
          (tc.tags !== null && tc.tags.length > 0 && selectedTags.value.some(tagId => 
            tc.tags!.some(tag => tag.id === tagId)
          ));

        return matchesPriority && matchesPlatform && matchesTestCaseType && matchesFolder && matchesTag;
      })
      .map(tc => tc.id);
  }

  const formData = {
    name: name.value,
    description: description.value,
    environment: environment.value || null,
    build: build.value || null,
    release: release.value || null,
    selectionType: activeTab.value,
    type: 'general', // Set the type for general test runs
    testCaseIds: testCaseIdsToSend,
    ...(activeTab.value === 'dynamic' && {
      dynamicFilters: dynamicFiltersToSend
    })
  };

  emit('submit', formData);
};

const selectedTestCaseCount = computed(() => {
  if (activeTab.value === 'specific') {
    return selectedTestCases.value.length;
  } else if (activeTab.value === 'dynamic') {
    return getSelectedTestCaseIds().length;
  } else {
    return testCases.value.length; // For 'all' tab, show total test cases
  }
});

const getSelectedTestCaseIds = () => {
  if (activeTab.value === 'all') {
    return testCases.value.map(tc => tc.id);
  }

  if (activeTab.value === 'specific') {
    return selectedTestCases.value;
  }

  // Dynamic filtering
  if (activeTab.value === 'dynamic') {
    return testCases.value
      .filter(tc => {
        const matchesPriority = selectedPriorities.value.length === 0 ||
          selectedPriorities.value.includes(tc.priority);
        const matchesPlatform = selectedPlatforms.value.length === 0 ||
          selectedPlatforms.value.includes(tc.platform);
        const matchesTestCaseType = selectedTestCaseTypes.value.length === 0 ||
          selectedTestCaseTypes.value.includes(tc.testCaseType);
        const matchesFolder = selectedFolders.value.length === 0 ||
          selectedFolders.value.includes(tc.folderId || 'none');
        const matchesTag = selectedTags.value.length === 0 || 
          (selectedTags.value.includes('none') && (!tc.tags || tc.tags.length === 0)) ||
          (tc.tags !== null && tc.tags.length > 0 && selectedTags.value.some(tagId => 
            tc.tags!.some(tag => tag.id === tagId)
          ));

        return matchesPriority && matchesPlatform && matchesTestCaseType && matchesFolder && matchesTag;
      })
      .map(tc => tc.id);
  }
  return [];
};

const toggleTestCase = (testCaseId: string) => {
  const index = selectedTestCases.value.indexOf(testCaseId);
  if (index === -1) {
    selectedTestCases.value.push(testCaseId);
  } else {
    selectedTestCases.value.splice(index, 1);
  }
};

const toggleFolder = (folderId: string) => {
  const index = selectedFolders.value.indexOf(folderId);
  if (index === -1) {
    selectedFolders.value.push(folderId);
  } else {
    selectedFolders.value.splice(index, 1);
  }
};

const toggleTag = (tagId: string) => {
  const index = selectedTags.value.indexOf(tagId);
  if (index === -1) {
    selectedTags.value.push(tagId);
  } else {
    selectedTags.value.splice(index, 1);
  }
};

const togglePriority = (priority: string) => {
  const index = selectedPriorities.value.indexOf(priority);
  if (index === -1) {
    selectedPriorities.value.push(priority);
  } else {
    selectedPriorities.value.splice(index, 1);
  }
};

const togglePlatform = (platform: string) => {
  const index = selectedPlatforms.value.indexOf(platform);
  if (index === -1) {
    selectedPlatforms.value.push(platform);
  } else {
    selectedPlatforms.value.splice(index, 1);
  }
};

const toggleTestCaseType = (testCaseType: string) => {
  const index = selectedTestCaseTypes.value.indexOf(testCaseType);
  if (index === -1) {
    selectedTestCaseTypes.value.push(testCaseType);
  } else {
    selectedTestCaseTypes.value.splice(index, 1);
  }
};

watch(
  () => props.editingTestRun,
  (newValue) => {
    if (newValue) {
      name.value = newValue.name;
      description.value = newValue.description || '';
      environment.value = newValue.environment || '';
      build.value = newValue.build || '';
      release.value = newValue.release || '';
      activeTab.value = newValue.selectionType;

      // Populate selectedTestCases if selectionType is 'specific'
      if (newValue.selectionType === 'specific' && (newValue as any).selectedTestCaseIds) {
        selectedTestCases.value = (newValue as any).selectedTestCaseIds;
      }

      // Populate filters for dynamic type
      if (newValue.selectionType === 'dynamic' && newValue.selectedTestCaseIds?.length) {
        try {
          const dynamicFilters = JSON.parse(newValue.selectedTestCaseIds[0]);
          selectedPriorities.value = dynamicFilters.priorities || [];
          selectedPlatforms.value = dynamicFilters.platforms || [];
          selectedTestCaseTypes.value = dynamicFilters.testCaseTypes || [];
          selectedFolders.value = dynamicFilters.folders || [];
          selectedTags.value = dynamicFilters.tags || [];
        } catch (e) {
          console.error('Failed to parse dynamic filters:', e);
          selectedPriorities.value = [];
          selectedPlatforms.value = [];
          selectedTestCaseTypes.value = [];
          selectedFolders.value = [];
          selectedTags.value = [];
        }
      } else {
        selectedPriorities.value = [];
        selectedPlatforms.value = [];
        selectedTestCaseTypes.value = [];
        selectedFolders.value = [];
        selectedTags.value = [];
      }
    } else {
      resetForm();
    }
  },
  { immediate: true }
);

onMounted(() => {
  fetchTestCases();
  fetchFolders();
  fetchTags();
});
</script>

<template>
  <div v-if="show" class="modal-overlay">
    <div class="modal-content">
      <h3>{{ editingTestRun ? 'Edit' : 'Create' }} Test Run</h3>
      
      <form @submit.prevent="handleSubmit">
        <!-- Basic Information -->
        <div class="form-section">
          <h4>Basic Information</h4>
          
          <div class="form-group">
            <label>Name</label>
            <input 
              v-model="name"
              type="text"
              required
              placeholder="Enter test run name"
            />
          </div>

          <div class="form-group">
            <label>Description</label>
            <textarea
              v-model="description"
              rows="3"
              placeholder="Enter test run description"
            ></textarea>
          </div>

          <div class="form-group">
            <label>Environment</label>
            <input 
              v-model="environment"
              type="text"
              placeholder="e.g., staging, production"
            />
          </div>

          <div class="form-group">
            <label>Build Version</label>
            <input 
              v-model="build"
              type="text"
              placeholder="e.g., v1.2.3"
            />
          </div>

          <div class="form-group">
            <label>Release Version</label>
            <input 
              v-model="release"
              type="text"
              placeholder="e.g., R2024.1"
            />
          </div>
        </div>

        <!-- Test Case Selection -->
        <div class="form-section">
          <h4>Test Case Selection</h4>
          
          <div class="tabs">
            <button 
              type="button"
              :class="['tab-button', { active: activeTab === 'all' }]"
              @click="activeTab = 'all'"
            >
              All Test Cases
            </button>
            <!-- <button 
              type="button"
              :class="['tab-button', { active: activeTab === 'specific' }]"
              @click="activeTab = 'specific'"
            >
              Specific Test Cases
            </button> -->
            <button 
              type="button"
              :class="['tab-button', { active: activeTab === 'dynamic' }]"
              @click="activeTab = 'dynamic'"
            >
              Dynamic Filtering
            </button>
          </div>

          <!-- All Test Cases Tab -->
          <div v-if="activeTab === 'all'" class="tab-content">
            <p class="info-text">
              All {{ testCases.length }} test cases will be included in this test run.
            </p>
          </div>

          <!-- Specific Test Cases Tab -->
          <div v-if="activeTab === 'specific'" class="tab-content">
            <div class="folder-tree">
              <div v-for="folder in folders" :key="folder.id" class="folder">
                <div class="folder-header">
                  <span class="folder-name">{{ folder.name }}</span>
                </div>
                <div class="folder-content">
                  <div 
                    v-for="testCase in testCases.filter(tc => tc.folderId === folder.id)"
                    :key="testCase.id"
                    class="test-case-item"
                  >
                    <label class="checkbox-label">
                      <input 
                        type="checkbox"
                        :checked="selectedTestCases.includes(testCase.id)"
                        @change="toggleTestCase(testCase.id)"
                      />
                      <span>TC-{{ testCase.tcId }}: {{ testCase.title }}</span>
                    </label>
                  </div>
                </div>
              </div>
              <!-- Uncategorized test cases -->
              <div class="folder">
                <div class="folder-header">
                  <span class="folder-name">Uncategorized</span>
                </div>
                <div class="folder-content">
                  <div 
                    v-for="testCase in testCases.filter(tc => !tc.folderId)"
                    :key="testCase.id"
                    class="test-case-item"
                  >
                    <label class="checkbox-label">
                      <input 
                        type="checkbox"
                        :checked="selectedTestCases.includes(testCase.id)"
                        @change="toggleTestCase(testCase.id)"
                      />
                      <span>TC-{{ testCase.tcId }}: {{ testCase.title }}</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- All Test Cases Tab -->
          <div v-if="activeTab === 'specific'" class="tab-content">
            <p class="filter-summary">
              Selected test cases: {{ selectedTestCaseCount }}
            </p>
          </div>

          <!-- Dynamic Filtering Tab -->
          <div v-if="activeTab === 'dynamic'" class="tab-content">
            <div class="filter-section">
              <div class="filter-group">
                <h5>Priority</h5>
                <div class="checkbox-group">
                  <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedPriorities.includes('low')"
                      @change="togglePriority('low')"
                    />
                    <span>Low</span>
                  </label>
                  <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedPriorities.includes('medium')"
                      @change="togglePriority('medium')"
                    />
                    <span>Medium</span>
                  </label>
                  <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedPriorities.includes('high')"
                      @change="togglePriority('high')"
                    />
                    <span>High</span>
                  </label>
                  <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedPriorities.includes('critical')"
                      @change="togglePriority('critical')"
                    />
                    <span>Critical</span>
                  </label>
                </div>
              </div>

              <div class="filter-group">
                <h5>Platform</h5>
                <div class="checkbox-group">
                  <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedPlatforms.includes('web')"
                      @change="togglePlatform('web')"
                    />
                    <span>Web</span>
                  </label>
                  <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedPlatforms.includes('mobile')"
                      @change="togglePlatform('mobile')"
                    />
                    <span>Mobile</span>
                  </label>
                  <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedPlatforms.includes('api')"
                      @change="togglePlatform('api')"
                    />
                    <span>API</span>
                  </label>
                  <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedPlatforms.includes('desktop')"
                      @change="togglePlatform('desktop')"
                    />
                    <span>Desktop</span>
                  </label>
                </div>
              </div>

              <div class="filter-group">
                <h5>Test Case Type</h5>
                <div class="checkbox-group">
                  <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedTestCaseTypes.includes('manual')"
                      @change="toggleTestCaseType('manual')"
                    />
                    <span>Manual</span>
                  </label>
                  <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedTestCaseTypes.includes('automation')"
                      @change="toggleTestCaseType('automation')"
                    />
                    <span>Automation</span>
                  </label>
                </div>
              </div>

              <div class="filter-group">
                <h5>Folders</h5>
                <div class="checkbox-group">
                  <label 
                    v-for="folder in folders"
                    :key="folder.id"
                    class="checkbox-label"
                  >
                    <input 
                      type="checkbox"
                      :checked="selectedFolders.includes(folder.id)"
                      @change="toggleFolder(folder.id)"
                    />
                    <span>{{ folder.name }}</span>
                  </label>
                  <!-- <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedFolders.includes('none')"
                      @change="toggleFolder('none')"
                    />
                    <span>Uncategorized</span>
                  </label> -->
                </div>
              </div>

              <div class="filter-group">
                <h5>Tags</h5>
                <div class="checkbox-group">
                  <label 
                    v-for="tag in tags"
                    :key="tag.id"
                    class="checkbox-label"
                  >
                    <input 
                      type="checkbox"
                      :checked="selectedTags.includes(tag.id)"
                      @change="toggleTag(tag.id)"
                    />
                    <span>{{ tag.name }}</span>
                  </label>
                  <!-- <label class="checkbox-label">
                    <input 
                      type="checkbox"
                      :checked="selectedTags.includes('none')"
                      @change="toggleTag('none')"
                    />
                    <span>Uncategorized</span>
                  </label> -->
                </div>
              </div>

            </div>

            <div class="filter-summary">
              <p>
                Selected test cases: {{ selectedTestCaseCount }}
              </p>
            </div>
          </div>
        </div>

        <div class="modal-actions">
          <button type="button" class="cancel-button" @click="closeModal">
            Cancel
          </button>
          <button type="submit" class="submit-button" :disabled="loading">
            {{ editingTestRun ? 'Update' : 'Create' }}
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  padding: 24px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;

  h3 {
    margin: 0 0 24px;
    font-size: 20px;
    color: #374151;
  }
}

.form-section {
  margin-bottom: 32px;

  h4 {
    font-size: 16px;
    color: #374151;
    margin-bottom: 16px;
  }
}

.form-group {
  margin-bottom: 16px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
  }

  input,
  textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }

  textarea {
    resize: vertical;
  }
}

.tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.tab-button {
  padding: 8px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  background: none;
  color: #374151;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background-color: #f9fafb;
  }

  &.active {
    background-color: #e94560;
    color: white;
    border-color: #e94560;
  }
}

.tab-content {
  background-color: #f9fafb;
  border-radius: 6px;
  padding: 16px;
}

.info-text {
  color: #6b7280;
  font-size: 14px;
}

.folder-tree {
  max-height: 400px;
  overflow-y: auto;
}

.folder {
  margin-bottom: 16px;

  .folder-header {
    font-weight: 500;
    color: #374151;
    margin-bottom: 8px;
  }

  .folder-content {
    padding-left: 16px;
  }
}

.test-case-item {
  margin-bottom: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #374151;

  input[type="checkbox"] {
    width: 16px;
    height: 16px;
  }
}

.filter-section {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 24px;
}

.filter-group {
  h5 {
    font-size: 14px;
    color: #374151;
    margin-bottom: 12px;
  }

  .checkbox-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
}

.filter-summary {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
  font-size: 14px;
  color: #6b7280;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;

  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
  }

  .cancel-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;

    &:hover {
      background-color: #e5e7eb;
    }
  }

  .submit-button {
    background-color: #e94560;
    color: white;
    border: none;

    &:hover:not(:disabled) {
      background-color: #d63553;
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}
</style>