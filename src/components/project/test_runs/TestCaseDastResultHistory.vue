<template>
    <div v-if="show" class="modal-overlay">
      <div class="modal-content" :class="{ 'bulk-update-modal': isBulkUpdate }">
        <div class="modal-header">
          <h3 class="modal-title">{{ isBulkUpdate ? `Bulk Update (${selectedTestResults?.length || 0} Security Test Results)` : 'Security Test Case History' }}</h3>
          <div class="header-actions">
          </div>
          <button class="close-button" @click="closeModal">
            <span class="material-icons">x</span>
          </button>
        </div>

        <div class="modal-body">
          <div v-if="loading" class="loading">
            Loading...
          </div>

          <div v-else-if="error" class="error-message">
            {{ error }}
          </div>

          <div v-else-if="isBulkUpdate" class="bulk-update-container">
            <div class="bulk-update-header">
              <h4>Bulk Update {{ selectedTestResults?.length || 0 }} Security Test Results</h4>
              <p class="bulk-update-description">
                Update the security findings for all selected DAST test results at once.
              </p>
            </div>

            <div class="bulk-update-form">
              <div class="form-group">
                <label for="status-select">Status</label>
                <select id="status-select" v-model="editableStatus" class="status-select" :class="{ 'status-warning-border': editableStatus === 'New' || !editableStatus }">
                  <option value="" disabled>Select a status</option>
                  <option value="New">New</option>
                  <option value="Open">Open</option>
                  <option value="In Progress">In Progress</option>
                  <option value="Fixed/Remediated">Fixed/Remediated</option>
                  <option value="Verified/Re-tested">Verified/Re-tested</option>
                  <option value="Closed">Closed</option>
                  <option value="False Positive">False Positive</option>
                  <option value="Accepted Risk / Waived">Accepted Risk / Waived</option>
                  <option value="Duplicate">Duplicate</option>
                </select>
              </div>

              <div class="form-group">
                <label for="vulnerability-description">Vulnerability Description</label>
                <textarea
                  id="vulnerability-description"
                  v-model="editableVulnerabilityDescription"
                  class="edit-textarea"
                  placeholder="Enter vulnerability description for all selected test results"
                ></textarea>
              </div>

              <div class="form-group">
                <label for="notes">Notes</label>
                <textarea
                  id="notes"
                  v-model="editableNotes"
                  class="edit-textarea"
                  placeholder="Enter notes for all selected test results"
                ></textarea>
              </div>

              <div class="form-group">
                <label for="original-severity">Original Severity</label>
                <select id="original-severity" v-model="editableOriginalSeverity" class="severity-select">
                  <option value="">Select original severity</option>
                  <option value="Critical">Critical</option>
                  <option value="High">High</option>
                  <option value="Medium">Medium</option>
                  <option value="Low">Low</option>
                  <option value="Informational">Informational</option>
                </select>
              </div>

              <div class="form-group">
                <label for="adjusted-severity">Adjusted Severity</label>
                <select id="adjusted-severity" v-model="editableAdjustedSeverity" class="severity-select">
                  <option value="">Select adjusted severity</option>
                  <option value="Critical">Critical</option>
                  <option value="High">High</option>
                  <option value="Medium">Medium</option>
                  <option value="Low">Low</option>
                  <option value="Informational">Informational</option>
                </select>
              </div>

              <div class="form-group">
                <label for="affected-urls">Affected URL(s)</label>
                <textarea
                  id="affected-urls"
                  v-model="editableAffectedUrls"
                  class="edit-textarea"
                  placeholder="Enter affected URLs (one per line)"
                ></textarea>
              </div>

              <div class="form-group">
                <label for="request-response">Request & Response</label>
                <textarea
                  id="request-response"
                  v-model="editableRequestResponse"
                  class="edit-textarea large-textarea"
                  placeholder="Enter request and response details"
                ></textarea>
              </div>

              <div class="form-group">
                <label for="remediation-guidance">Remediation Guidance</label>
                <textarea
                  id="remediation-guidance"
                  v-model="editableRemediationGuidance"
                  class="edit-textarea"
                  placeholder="Enter remediation guidance"
                ></textarea>
              </div>

              <div class="edit-actions">
                <button
                  class="save-button"
                  @click="saveChanges"
                  :disabled="!editableStatus"
                  :class="{ 'disabled-button': !editableStatus }"
                >
                  <span class="save-icon">💾</span> Save Changes
                </button>
                <button class="cancel-button" @click="closeModal">
                  Cancel
                </button>
              </div>
            </div>
          </div>

          <div v-else class="modal-layout" :class="{ 'three-column': showUpdatePanel, 'two-column': !showUpdatePanel }">
            <!-- Left Column: Test Case Details -->
            <div class="test-case-details-column">
              <div class="test-case-info">
                <h4 class="test-case-title">
                  🔒 TC-{{ tcId }} {{ testCaseTitle }}
                </h4>
                <div class="security-badge">
                  <span class="badge-text">Security DAST Test</span>
                </div>
                <div class="test-case-properties">
                  <div class="test-case-property">
                    <strong>Precondition:</strong>
                    <p v-html="formatSteps(testCasePrecondition)"></p>
                  </div>
                  <div class="test-case-property">
                    <strong>Steps:</strong>
                    <p v-html="formatSteps(testCaseSteps)"></p>
                  </div>
                  <div class="test-case-property">
                    <strong>Expectation:</strong>
                    <p v-html="formatSteps(testCaseExpectation)"></p>
                  </div>
                  <div class="test-case-property">
                    <strong>Type:</strong>
                    <span>{{ tcType }}</span>
                  </div>
                  <div class="test-case-property">
                    <strong>Priority:</strong>
                    <span>{{ testCasePriority }}</span>
                  </div>
                  <div class="test-case-property">
                    <strong>Platform:</strong>
                    <span>{{ testCasePlatform }}</span>
                  </div>
                  <div class="test-case-property">
                    <strong>Test Case Type:</strong>
                    <span>{{ testCaseType }}</span>
                  </div>
                  <div class="test-case-property">
                    <strong>Tags:</strong>
                    <span v-if="testCaseTags && testCaseTags.length > 0">
                      <span
                        v-for="tag in testCaseTags"
                        :key="tag.id"
                        class="tag-badge"
                      >
                        {{ tag.name }}
                      </span>
                    </span>
                    <span v-else>-</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Middle Column: History List -->
            <div class="history-column">
              <div class="previous-changes-header">
                <div class="history-section-title">
                  <h4>Security Test Result History</h4>
                </div>

                <!-- Right side: Item count and Update button -->
                <div class="history-header-right">
                  <div class="history-count" v-if="totalItems > 0">
                    {{ totalItems }} result{{ totalItems !== 1 ? 's' : '' }}
                  </div>
                  <button
                    class="toggle-update-button"
                    @click="toggleUpdatePanel"
                    :class="{ 'active': showUpdatePanel }"
                  >
                    <span class="update-icon">🔒</span>
                    {{ showUpdatePanel ? 'Hide Update' : 'Update Security Result' }}
                  </button>
                </div>
              </div>

              <div v-if="history.length === 0 && totalItems === 0" class="empty-state">
                No security test history available for this test case.
              </div>

              <!-- History Items -->
              <div class="history-items-list">
                <div v-for="item in history" :key="item.id" class="history-item" :class="{ 'current-item': item.isLatest }">
                  <div class="history-item-content" @click="startEditingWithData(item)" :title="'Click to edit this result'">
                    <!-- Left border indicator for status -->
                    <div :class="['status-indicator', getSecurityStatusClass(item.status)]"></div>

                    <!-- Main content area -->
                    <div class="history-item-main">
                      <!-- Header with status and timestamp -->
                      <div class="history-header">
                        <span
                          :class="['status-badge', getSecurityStatusClass(item.status)]"
                        >
                          {{ item.status }}
                        </span>
                        <span class="timestamp">
                          {{ new Date(item.createdAt).toLocaleString(undefined, {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric',
                            hour: 'numeric',
                            minute: 'numeric',
                            hour12: true
                          }) }}
                        </span>
                        <span class="created-by" style="margin-left: 12px; font-size: 12px; color: #888;">
                          Created by: {{ item.createdBy || '-' }}
                        </span>
                      </div>

                      <!-- Security Details section -->
                      <div class="history-details">
                        <div class="detail-group">
                          <label>Vulnerability Description:</label>
                          <div class="content-container">
                            <p class="truncated-content" :class="{ 'empty-content': !item.vulnerabilityDescription }">
                              {{ cleanText(item.vulnerabilityDescription) || 'None' }}
                            </p>
                            <button v-if="item.vulnerabilityDescription && cleanText(item.vulnerabilityDescription).length > 100"
                              class="show-more-button"
                              @click="showTextModal('Vulnerability Description', cleanText(item.vulnerabilityDescription))">
                              Show more
                            </button>
                          </div>
                        </div>

                        <div class="detail-group">
                          <label>Notes:</label>
                          <div class="content-container">
                            <p class="truncated-content" :class="{ 'empty-content': !item.notes }">
                              {{ cleanText(item.notes) || 'None' }}
                            </p>
                            <button v-if="item.notes && cleanText(item.notes).length > 100"
                              class="show-more-button"
                              @click="showTextModal('Notes', cleanText(item.notes))">
                              Show more
                            </button>
                          </div>
                        </div>

                        <div class="detail-group" v-if="item.originalSeverity || item.adjustedSeverity">
                          <label>Severity:</label>
                          <div class="severity-info">
                            <span v-if="item.originalSeverity" :class="['severity-badge', getSeverityClass(item.originalSeverity)]">
                              Original: {{ item.originalSeverity }}
                            </span>
                            <span v-if="item.adjustedSeverity" :class="['severity-badge', getSeverityClass(item.adjustedSeverity)]">
                              Adjusted: {{ item.adjustedSeverity }}
                            </span>
                          </div>
                        </div>

                        <div class="detail-group" v-if="item.affectedUrls">
                          <label>Affected URLs:</label>
                          <div class="content-container">
                            <p class="truncated-content">
                              {{ cleanText(item.affectedUrls) || 'None' }}
                            </p>
                            <button v-if="item.affectedUrls && cleanText(item.affectedUrls).length > 100"
                              class="show-more-button"
                              @click="showTextModal('Affected URLs', cleanText(item.affectedUrls))">
                              Show more
                            </button>
                          </div>
                        </div>

                        <div class="detail-group" v-if="item.requestResponse">
                          <label>Request & Response:</label>
                          <div class="content-container">
                            <p class="truncated-content code-content">
                              {{ cleanText(item.requestResponse) || 'None' }}
                            </p>
                            <button v-if="item.requestResponse && cleanText(item.requestResponse).length > 100"
                              class="show-more-button"
                              @click="showTextModal('Request & Response', cleanText(item.requestResponse))">
                              Show more
                            </button>
                          </div>
                        </div>

                        <div class="detail-group" v-if="item.remediationGuidance">
                          <label>Remediation Guidance:</label>
                          <div class="content-container">
                            <p class="truncated-content">
                              {{ cleanText(item.remediationGuidance) || 'None' }}
                            </p>
                            <button v-if="item.remediationGuidance && cleanText(item.remediationGuidance).length > 100"
                              class="show-more-button"
                              @click="showTextModal('Remediation Guidance', cleanText(item.remediationGuidance))">
                              Show more
                            </button>
                          </div>
                        </div>

                        <div class="detail-group" v-if="item.logsSecurityUrl">
                          <label>Security Report:</label>
                          <div class="content-container">
                            <button class="security-report-button" @click="openSecurityReportModal(item.id)">
                              📄 View HTML Security Report
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

              </div>

              <!-- Fixed pagination at bottom corner -->
              <div v-if="totalPages > 1" class="pagination-container fixed-pagination">
                <div class="pagination-controls">
                  <button
                    class="pagination-button"
                    :disabled="currentPage === 1"
                    @click="goToPage(1)"
                    title="First Page"
                  >
                    «
                  </button>
                  <button
                    class="pagination-button"
                    :disabled="currentPage === 1"
                    @click="goToPage(currentPage - 1)"
                    title="Previous Page"
                  >
                    ‹
                  </button>

                  <div class="pagination-pages">
                    <template v-for="page in displayedPages" :key="page">
                      <span v-if="page === '...'" class="pagination-ellipsis">...</span>
                      <button
                        v-else
                        class="pagination-page-button"
                        :class="{ active: page === currentPage }"
                        @click="goToPage(page as number)"
                      >
                        {{ page }}
                      </button>
                    </template>
                  </div>

                  <button
                    class="pagination-button"
                    :disabled="currentPage === totalPages"
                    @click="goToPage(currentPage + 1)"
                    title="Next Page"
                  >
                    ›
                  </button>
                  <button
                    class="pagination-button"
                    :disabled="currentPage === totalPages"
                    @click="goToPage(totalPages)"
                    title="Last Page"
                  >
                    »
                  </button>
                </div>
              </div>
            </div>

            <!-- Right Column: Update Security Test Result (conditionally shown) -->
            <div v-if="showUpdatePanel" class="update-result-column">
              <div class="current-result-header">
                <h4>Update Security Test Result</h4>
              </div>

              <div class="current-result">
                <div v-if="!isEditing" class="result-view-mode">
                  <div class="result-card">
                    <div class="result-row">
                      <div class="result-label">Last Status</div>
                      <div class="result-value">
                        <span :class="['status-badge', getSecurityStatusClass(localStatus)]">
                          {{ localStatus }}
                        </span>
                      </div>
                    </div>

                    <div class="result-row">
                      <div class="result-label">Last Updated</div>
                      <div class="result-value timestamp">
                        {{ testCaseUpdatedAt ?
                          new Date(testCaseUpdatedAt).toLocaleString(undefined, {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric',
                            hour: 'numeric',
                            minute: 'numeric',
                            hour12: true,
                            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
                          }) :
                          new Date(testCaseCreatedAt).toLocaleString(undefined, {
                            year: 'numeric',
                            month: 'numeric',
                            day: 'numeric',
                            hour: 'numeric',
                            minute: 'numeric',
                            hour12: true,
                            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
                          }) }}
                      </div>
                    </div>

                    <div class="result-row">
                      <div class="result-label">Vulnerability Description</div>
                      <div class="result-value result-text">
                        {{ cleanText(localVulnerabilityDescription) || '-' }}
                      </div>
                    </div>

                    <div class="result-row">
                      <div class="result-label">Notes</div>
                      <div class="result-value result-text">
                        {{ cleanText(localNotes) || '-' }}
                      </div>
                    </div>

                    <div class="result-row" v-if="localOriginalSeverity || localAdjustedSeverity">
                      <div class="result-label">Severity</div>
                      <div class="result-value">
                        <div class="severity-info">
                          <span v-if="localOriginalSeverity" :class="['severity-badge', getSeverityClass(localOriginalSeverity)]">
                            Original: {{ localOriginalSeverity }}
                          </span>
                          <span v-if="localAdjustedSeverity" :class="['severity-badge', getSeverityClass(localAdjustedSeverity)]">
                            Adjusted: {{ localAdjustedSeverity }}
                          </span>
                        </div>
                      </div>
                    </div>

                    <div class="result-row" v-if="localAffectedUrls">
                      <div class="result-label">Affected URLs</div>
                      <div class="result-value result-text">
                        {{ cleanText(localAffectedUrls) || '-' }}
                      </div>
                    </div>

                    <div class="result-row" v-if="localRequestResponse">
                      <div class="result-label">Request & Response</div>
                      <div class="result-value result-text code-content">
                        {{ cleanText(localRequestResponse) || '-' }}
                      </div>
                    </div>

                    <div class="result-row" v-if="localRemediationGuidance">
                      <div class="result-label">Remediation Guidance</div>
                      <div class="result-value result-text">
                        {{ cleanText(localRemediationGuidance) || '-' }}
                      </div>
                    </div>

                    <!-- Security Report Section in View Mode -->
                    <div class="result-row" v-if="props.testCaseLogsSecurityUrl">
                      <div class="result-label">Security Report</div>
                      <div class="result-value">
                        <button class="view-report-button small" @click="openSecurityReportModal(props.testResultId)">
                          📄 View HTML Security Report
                        </button>
                      </div>
                    </div>

                    <!-- Vulnerability Counts in View Mode -->
                    <div class="result-row" v-if="props.testCaseHighCount || props.testCaseMediumCount || props.testCaseLowCount || props.testCaseInformationalCount || props.testCaseFalsePositiveCount">
                      <div class="result-label">Vulnerability Counts</div>
                      <div class="result-value">
                        <div class="mini-vuln-summary">
                          <span class="mini-count high" v-if="props.testCaseHighCount">
                            {{ props.testCaseHighCount }} High
                          </span>
                          <span class="mini-count medium" v-if="props.testCaseMediumCount">
                            {{ props.testCaseMediumCount }} Medium
                          </span>
                          <span class="mini-count low" v-if="props.testCaseLowCount">
                            {{ props.testCaseLowCount }} Low
                          </span>
                          <span class="mini-count info" v-if="props.testCaseInformationalCount">
                            {{ props.testCaseInformationalCount }} Info
                          </span>
                          <span class="mini-count false-positive" v-if="props.testCaseFalsePositiveCount">
                            {{ props.testCaseFalsePositiveCount }} False Positive
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div v-else class="result-edit-mode">
                  <div class="edit-form">
                    <div class="form-group">
                      <label for="status-select">Status</label>
                      <select id="status-select" v-model="editableStatus" class="status-select" :class="{ 'status-warning-border': !editableStatus }">
                        <option value="" disabled>Select a status</option>
                        <option value="New">New</option>
                        <option value="Open">Open</option>
                        <option value="In Progress">In Progress</option>
                        <option value="Fixed/Remediated">Fixed/Remediated</option>
                        <option value="Verified/Re-tested">Verified/Re-tested</option>
                        <option value="Closed">Closed</option>
                        <option value="False Positive">False Positive</option>
                        <option value="Accepted Risk / Waived">Accepted Risk / Waived</option>
                        <option value="Duplicate">Duplicate</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label for="vulnerability-description">Vulnerability Description</label>
                      <textarea
                        id="vulnerability-description"
                        v-model="editableVulnerabilityDescription"
                        class="edit-textarea"
                        placeholder="Enter vulnerability description"
                      ></textarea>
                    </div>

                    <div class="form-group">
                      <label for="notes">Notes</label>
                      <textarea
                        id="notes"
                        v-model="editableNotes"
                        class="edit-textarea"
                        placeholder="Enter notes"
                      ></textarea>
                    </div>

                    <div class="form-group">
                      <label for="original-severity">Original Severity</label>
                      <select id="original-severity" v-model="editableOriginalSeverity" class="severity-select">
                        <option value="">Select original severity</option>
                        <option value="Critical">Critical</option>
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                        <option value="Informational">Informational</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label for="adjusted-severity">Adjusted Severity</label>
                      <select id="adjusted-severity" v-model="editableAdjustedSeverity" class="severity-select">
                        <option value="">Select adjusted severity</option>
                        <option value="Critical">Critical</option>
                        <option value="High">High</option>
                        <option value="Medium">Medium</option>
                        <option value="Low">Low</option>
                        <option value="Informational">Informational</option>
                      </select>
                    </div>

                    <div class="form-group">
                      <label for="affected-urls">Affected URL(s)</label>
                      <textarea
                        id="affected-urls"
                        v-model="editableAffectedUrls"
                        class="edit-textarea"
                        placeholder="Enter affected URLs (one per line)"
                      ></textarea>
                    </div>

                    <div class="form-group">
                      <label for="request-response">Request & Response</label>
                      <textarea
                        id="request-response"
                        v-model="editableRequestResponse"
                        class="edit-textarea large-textarea"
                        placeholder="Enter request and response details"
                      ></textarea>
                    </div>

                    <div class="form-group">
                      <label for="remediation-guidance">Remediation Guidance</label>
                      <textarea
                        id="remediation-guidance"
                        v-model="editableRemediationGuidance"
                        class="edit-textarea"
                        placeholder="Enter remediation guidance"
                      ></textarea>
                    </div>

                    <!-- Vulnerability Count Fields -->
                    <div class="vulnerability-counts-section">
                      <h5 class="section-title">Vulnerability Counts</h5>

                      <div class="counts-grid">
                        <div class="form-group">
                          <label for="high-count">High Risk</label>
                          <input
                            id="high-count"
                            v-model.number="editableHighCount"
                            type="number"
                            min="0"
                            class="count-input high"
                            placeholder="0"
                          />
                        </div>

                        <div class="form-group">
                          <label for="medium-count">Medium Risk</label>
                          <input
                            id="medium-count"
                            v-model.number="editableMediumCount"
                            type="number"
                            min="0"
                            class="count-input medium"
                            placeholder="0"
                          />
                        </div>

                        <div class="form-group">
                          <label for="low-count">Low Risk</label>
                          <input
                            id="low-count"
                            v-model.number="editableLowCount"
                            type="number"
                            min="0"
                            class="count-input low"
                            placeholder="0"
                          />
                        </div>

                        <div class="form-group">
                          <label for="informational-count">Informational</label>
                          <input
                            id="informational-count"
                            v-model.number="editableInformationalCount"
                            type="number"
                            min="0"
                            class="count-input informational"
                            placeholder="0"
                          />
                        </div>

                        <div class="form-group">
                          <label for="false-positive-count">False Positive</label>
                          <input
                            id="false-positive-count"
                            v-model.number="editableFalsePositiveCount"
                            type="number"
                            min="0"
                            class="count-input false-positive"
                            placeholder="0"
                          />
                        </div>
                      </div>
                    </div>

                    <div class="edit-actions">
                      <button
                        class="save-button"
                        @click="saveChanges"
                        :disabled="!editableStatus"
                        :class="{ 'disabled-button': !editableStatus }"
                      >
                        <span class="save-icon">💾</span> Save Changes
                      </button>
                      <button class="cancel-button" @click="cancelEditing">
                        Cancel
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Text Modal for Show More -->
      <div v-if="showTextModalFlag" class="text-modal-overlay" @click="closeTextModal">
        <div class="text-modal-content" @click.stop>
          <div class="text-modal-header">
            <h3>{{ textModalTitle }}</h3>
            <button class="text-modal-close-button" @click="closeTextModal">
              <span class="material-icons">×</span>
            </button>
          </div>
          <div class="text-modal-body">
            <pre class="text-modal-text">{{ textModalContent }}</pre>
          </div>
        </div>
      </div>

      <!-- Security Report Modal -->
      <div v-if="showSecurityReportModal" class="security-modal-overlay" @click="closeSecurityReportModal">
        <div class="security-modal-content" @click.stop>
          <div class="security-modal-header">
            <h3>Security Report</h3>
            <button class="security-modal-close-button" @click="closeSecurityReportModal">
              <span class="material-icons">×</span>
            </button>
          </div>
          <div class="security-modal-body">
            <div v-if="securityReportLoading" class="security-report-loading">
              <div class="loading-spinner"></div>
              <p>Loading security report...</p>
            </div>
            <div v-else-if="securityReportError" class="security-report-error">
              <p>{{ securityReportError }}</p>
            </div>
            <div v-else-if="securityReportHtml"
                 class="security-report-content"
                 v-html="securityReportHtml">
            </div>
            <div v-else class="security-report-empty">
              <p>No security report content available.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup lang="ts">
  import { ref, onMounted, watch, computed } from 'vue';
  import axios from 'axios';

  interface SecurityTestResult {
    id: string;
    status: string;
    vulnerabilityDescription: string | null;
    notes: string | null;
    originalSeverity: string | null;
    adjustedSeverity: string | null;
    affectedUrls: string | null;
    requestResponse: string | null;
    remediationGuidance: string | null;
    logsSecurityUrl: string | null;
    highCount: number | null;
    mediumCount: number | null;
    lowCount: number | null;
    informationalCount: number | null;
    falsePositiveCount: number | null;
    createdAt: string;
    sequence: number;
    isLatest: boolean;
    createdBy?: string | null;
  }

  interface Tag {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  }

  const props = defineProps<{
    show: boolean;
    projectId: string;
    testRunId: string;
    testResultId: string;
    testCaseTitle: string;
    testCaseType: string;
    tcType: string;
    testCasePriority: string;
    testCasePlatform: string;
    testCaseTags: Array<Tag>;
    testCasePrecondition: string;
    testCaseSteps: string;
    testCaseExpectation: string;
    tcId: string | number;
    testCaseStatus: string;
    testCaseNotes: string | null;
    testCaseVulnerabilityDescription: string | null;
    testCaseOriginalSeverity: string | null;
    testCaseAdjustedSeverity: string | null;
    testCaseAffectedUrls: string | null;
    testCaseRequestResponse: string | null;
    testCaseRemediationGuidance: string | null;
    testCaseLogsSecurityUrl?: string | null;
    testCaseHighCount?: number | null;
    testCaseMediumCount?: number | null;
    testCaseLowCount?: number | null;
    testCaseInformationalCount?: number | null;
    testCaseFalsePositiveCount?: number | null;
    testCaseUpdatedAt: string;
    testCaseCreatedAt: string;
    isBulkUpdate?: boolean;
    selectedTestResults?: SecurityTestResult[];
  }>();

  const emit = defineEmits<{
    'close': [];
    'updated': [updatedData: {
      status: string;
      vulnerabilityDescription: string | null;
      notes: string | null;
      originalSeverity: string | null;
      adjustedSeverity: string | null;
      affectedUrls: string | null;
      requestResponse: string | null;
      remediationGuidance: string | null;
    }];
    'refresh': [];
  }>();

  const history = ref<SecurityTestResult[]>([]);
  const loading = ref(false);
  const error = ref('');
  const isEditing = ref(false);
  const editableStatus = ref('');
  const editableVulnerabilityDescription = ref<string | null>('');
  const editableNotes = ref<string | null>('');
  const editableOriginalSeverity = ref<string | null>('');
  const editableAdjustedSeverity = ref<string | null>('');
  const editableAffectedUrls = ref<string | null>('');
  const editableRequestResponse = ref<string | null>('');
  const editableRemediationGuidance = ref<string | null>('');
  const editableLogsSecurityUrl = ref<string | null>('');
  const editableHighCount = ref<number | null>(null);
  const editableMediumCount = ref<number | null>(null);
  const editableLowCount = ref<number | null>(null);
  const editableInformationalCount = ref<number | null>(null);
  const editableFalsePositiveCount = ref<number | null>(null);
  const showUpdatePanel = ref(false);

  const projectId = props.projectId;
  const testRunId = props.testRunId;
  const testResultId = ref(props.testResultId);
  const localStatus = ref(props.testCaseStatus);
  const localVulnerabilityDescription = ref(props.testCaseVulnerabilityDescription);
  const localNotes = ref(props.testCaseNotes);
  const localOriginalSeverity = ref(props.testCaseOriginalSeverity);
  const localAdjustedSeverity = ref(props.testCaseAdjustedSeverity);
  const localAffectedUrls = ref(props.testCaseAffectedUrls);
  const localRequestResponse = ref(props.testCaseRequestResponse);
  const localRemediationGuidance = ref(props.testCaseRemediationGuidance);

  // Text Modal for Show More
  const showTextModalFlag = ref(false);
  const textModalTitle = ref('');
  const textModalContent = ref('');

  function showTextModal(title: string, content: string) {
    textModalTitle.value = title;
    textModalContent.value = content;
    showTextModalFlag.value = true;
  }

  function closeTextModal() {
    showTextModalFlag.value = false;
    textModalTitle.value = '';
    textModalContent.value = '';
  }

  // Pagination
  const currentPage = ref(1);
  const pageSize = ref(3);
  const totalItems = ref(0);
  const totalPages = computed(() => Math.ceil(totalItems.value / pageSize.value));

  // Calculate which page numbers to display
  const displayedPages = computed(() => {
    if (totalPages.value <= 3) {
      return Array.from({ length: totalPages.value }, (_, i) => i + 1);
    }

    const pages: (number | string)[] = [currentPage.value];

    if (currentPage.value > 1) {
      pages.unshift(currentPage.value - 1);
    }

    if (currentPage.value < totalPages.value) {
      pages.push(currentPage.value + 1);
    }

    if (pages.length < 3 && currentPage.value > 2) {
      pages.unshift(currentPage.value - 2);
    }

    if (pages.length < 3 && currentPage.value < totalPages.value - 1) {
      pages.push(currentPage.value + 2);
    }

    if (typeof pages[0] === 'number' && pages[0] > 1) {
      pages.unshift('...');
    }

    const lastPage = pages[pages.length - 1];
    if (typeof lastPage === 'number' && lastPage < totalPages.value) {
      pages.push('...');
    }

    return pages;
  });

  const toggleUpdatePanel = () => {
    showUpdatePanel.value = !showUpdatePanel.value;
    if (showUpdatePanel.value) {
      startEditingEmpty();
    } else {
      cancelEditing();
    }
  };

  const startEditingEmpty = () => {
    isEditing.value = true;
    // Initialize with empty values for new manual entry
    editableStatus.value = 'New';
    editableVulnerabilityDescription.value = '';
    editableNotes.value = '';
    editableOriginalSeverity.value = '';
    editableAdjustedSeverity.value = '';
    editableAffectedUrls.value = '';
    editableRequestResponse.value = '';
    editableRemediationGuidance.value = '';
    editableLogsSecurityUrl.value = '';
    editableHighCount.value = null;
    editableMediumCount.value = null;
    editableLowCount.value = null;
    editableInformationalCount.value = null;
    editableFalsePositiveCount.value = null;
  };

  const startEditingWithData = (item: SecurityTestResult) => {
    isEditing.value = true;
    showUpdatePanel.value = true;
    // Initialize with existing data from selected history item
    editableStatus.value = item.status || 'New';
    editableVulnerabilityDescription.value = item.vulnerabilityDescription || '';
    editableNotes.value = item.notes || '';
    editableOriginalSeverity.value = item.originalSeverity || '';
    editableAdjustedSeverity.value = item.adjustedSeverity || '';
    editableAffectedUrls.value = item.affectedUrls || '';
    editableRequestResponse.value = item.requestResponse || '';
    editableRemediationGuidance.value = item.remediationGuidance || '';
    editableLogsSecurityUrl.value = item.logsSecurityUrl || '';
    editableHighCount.value = item.highCount || null;
    editableMediumCount.value = item.mediumCount || null;
    editableLowCount.value = item.lowCount || null;
    editableInformationalCount.value = item.informationalCount || null;
    editableFalsePositiveCount.value = item.falsePositiveCount || null;
  };

  const cancelEditing = () => {
    isEditing.value = false;
    editableStatus.value = '';
    editableVulnerabilityDescription.value = '';
    editableNotes.value = '';
    editableOriginalSeverity.value = '';
    editableAdjustedSeverity.value = '';
    editableAffectedUrls.value = '';
    editableRequestResponse.value = '';
    editableRemediationGuidance.value = '';
  };

  const saveChanges = async () => {
    if (!editableStatus.value) {
      alert('Please select a status');
      return;
    }

    try {
      loading.value = true;

      const updateData = {
        status: editableStatus.value,
        vulnerabilityDescription: editableVulnerabilityDescription.value || null,
        notes: editableNotes.value || null,
        originalSeverity: editableOriginalSeverity.value || null,
        adjustedSeverity: editableAdjustedSeverity.value || null,
        affectedUrls: editableAffectedUrls.value || null,
        requestResponse: editableRequestResponse.value || null,
        remediationGuidance: editableRemediationGuidance.value || null,
        highCount: editableHighCount.value,
        mediumCount: editableMediumCount.value,
        lowCount: editableLowCount.value,
        informationalCount: editableInformationalCount.value,
        falsePositiveCount: editableFalsePositiveCount.value,
      };

      if (props.isBulkUpdate && props.selectedTestResults) {
        // Bulk update
        const testResultIds = props.selectedTestResults.map(result => result.id);
        await axios.put(
          `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results/bulk-update`,
          {
            testResultIds,
            ...updateData
          }
        );
      } else {
        // Single update
        await axios.patch(
          `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results/${testResultId.value}`,
          updateData
        );
      }

      // Update local state
      localStatus.value = editableStatus.value;
      localVulnerabilityDescription.value = editableVulnerabilityDescription.value;
      localNotes.value = editableNotes.value;
      localOriginalSeverity.value = editableOriginalSeverity.value;
      localAdjustedSeverity.value = editableAdjustedSeverity.value;
      localAffectedUrls.value = editableAffectedUrls.value;
      localRequestResponse.value = editableRequestResponse.value;
      localRemediationGuidance.value = editableRemediationGuidance.value;

      emit('updated', updateData);

      if (!props.isBulkUpdate) {
        await fetchHistory();
      }

      cancelEditing();

      if (props.isBulkUpdate) {
        closeModal();
      }

    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to save changes';
    } finally {
      loading.value = false;
    }
  };

  const fetchHistory = async () => {
    try {
      loading.value = true;
      const response = await axios.get(
        `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results/${testResultId.value}/history`
      );

      // Get history data - all test results for this test case in this test run
      const allHistory = response.data as SecurityTestResult[];

      // Store the total number of items for pagination
      totalItems.value = allHistory.length;

      // Calculate start and end indices for the current page
      const startIndex = (currentPage.value - 1) * pageSize.value;
      const endIndex = startIndex + pageSize.value;

      // Get the slice for the current page
      history.value = allHistory.slice(startIndex, endIndex);
    } catch (err: any) {
      error.value = err.response?.data?.message || 'Failed to fetch history';
    } finally {
      loading.value = false;
    }
  };

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages.value) {
      currentPage.value = page;
      fetchHistory();
    }
  };

  const closeModal = () => {
    emit('close');
  };

  const formatSteps = (text: string | null): string => {
    if (!text) return '';
    return text.replace(/\n/g, '<br>');
  };

  const cleanText = (text: string | null): string => {
    if (!text) return '';
    return text.replace(/<[^>]*>/g, '').trim();
  };

  const getSecurityStatusClass = (status: string): string => {
    const statusMap: { [key: string]: string } = {
      'New': 'new',
      'Open': 'open',
      'In Progress': 'in-progress',
      'Fixed/Remediated': 'fixed',
      'Verified/Re-tested': 'verified',
      'Closed': 'closed',
      'False Positive': 'false-positive',
      'Accepted Risk / Waived': 'accepted-risk',
      'Duplicate': 'duplicate'
    };
    return statusMap[status] || 'unknown';
  };

  const getSeverityClass = (severity: string): string => {
    const severityMap: { [key: string]: string } = {
      'Critical': 'critical',
      'High': 'high',
      'Medium': 'medium',
      'Low': 'low',
      'Informational': 'informational'
    };
    return severityMap[severity] || 'unknown';
  };

  // Security Report Modal
  const showSecurityReportModal = ref(false);
  const securityReportHtml = ref('');
  const securityReportLoading = ref(false);
  const securityReportError = ref('');

  const openSecurityReportModal = async (testResultId: string) => {
    showSecurityReportModal.value = true;
    securityReportLoading.value = true;
    securityReportError.value = '';
    securityReportHtml.value = '';

    try {
      // Fetch HTML content via authenticated API call
      // The global axios interceptor will automatically add the Authorization header
      const response = await axios.get(
        `${(import.meta as any).env.VITE_BACKEND_URL}/test-results/${testResultId}/security-report/html`,
        {
          headers: {
            'Accept': 'text/html'
          }
        }
      );
      securityReportHtml.value = response.data;
    } catch (error: any) {
      console.error('Failed to fetch security report:', error);
      securityReportError.value = error.response?.data?.message || 'Failed to load security report';
    } finally {
      securityReportLoading.value = false;
    }
  };

  const closeSecurityReportModal = () => {
    showSecurityReportModal.value = false;
    securityReportHtml.value = '';
    securityReportError.value = '';
  };

  // Watch for prop changes
  watch(() => props.testResultId, (newId) => {
    testResultId.value = newId;
    if (props.show && !props.isBulkUpdate) {
      fetchHistory();
    }
  });

  watch(() => props.show, (newShow) => {
    if (newShow && !props.isBulkUpdate) {
      fetchHistory();
    }
  });

  onMounted(() => {
    if (props.show && !props.isBulkUpdate) {
      fetchHistory();
    }
  });
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 12px;
  width: 95%;
  max-width: 1400px;
  height: 90%;
  max-height: 900px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.bulk-update-modal {
  max-width: 800px;
  height: auto;
  max-height: 80%;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f8fafc;
  border-radius: 12px 12px 0 0;

  .modal-title {
    font-size: 20px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }

  .close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }
}

.modal-body {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 200px;
  font-size: 16px;
  color: #6b7280;
}

.error-message {
  background-color: #fef2f2;
  border: 1px solid #fecaca;
  color: #dc2626;
  padding: 16px;
  border-radius: 8px;
  margin: 20px;
}

/* Bulk Update Styles */
.bulk-update-container {
  padding: 24px;
  overflow-y: auto;
}

.bulk-update-header {
  margin-bottom: 24px;

  h4 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 8px 0;
  }

  .bulk-update-description {
    color: #6b7280;
    font-size: 14px;
    margin: 0;
  }
}

.bulk-update-form {
  .form-group {
    margin-bottom: 20px;

    label {
      display: block;
      font-weight: 500;
      color: #374151;
      margin-bottom: 6px;
      font-size: 14px;
    }

    .status-select,
    .severity-select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
      background-color: white;

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }

      &.status-warning-border {
        border-color: #f59e0b;
      }
    }

    .edit-textarea {
      width: 100%;
      min-height: 80px;
      padding: 10px 12px;
      border: 1px solid #d1d5db;
      border-radius: 6px;
      font-size: 14px;
      font-family: inherit;
      resize: vertical;

      &.large-textarea {
        min-height: 120px;
      }

      &:focus {
        outline: none;
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
      }
    }
  }

  .edit-actions {
    display: flex;
    gap: 12px;
    margin-top: 24px;

    .save-button {
      background-color: #f59e0b;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 8px;
      transition: background-color 0.2s;

      &:hover:not(.disabled-button) {
        background-color: #d97706;
      }

      &.disabled-button {
        background-color: #9ca3af;
        cursor: not-allowed;
      }
    }

    .cancel-button {
      background-color: #6b7280;
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: background-color 0.2s;

      &:hover {
        background-color: #4b5563;
      }
    }
  }
}

/* Security Badge */
.security-badge {
  display: inline-block;
  background: linear-gradient(135deg, #f59e0b, #d97706);
  color: white;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  margin-bottom: 16px;
  box-shadow: 0 2px 4px rgba(245, 158, 11, 0.2);

  .badge-text {
    display: flex;
    align-items: center;
    gap: 4px;

    &:before {
      content: "🔒";
      font-size: 10px;
    }
  }
}

/* Security Status Classes */
.status-badge {
  &.new {
    background-color: #dbeafe;
    color: #1e40af;
  }

  &.open {
    background-color: #fef3c7;
    color: #d97706;
  }

  &.in-progress {
    background-color: #e0f2fe;
    color: #0277bd;
  }

  &.fixed {
    background-color: #dcfce7;
    color: #166534;
  }

  &.verified {
    background-color: #f0fdf4;
    color: #15803d;
  }

  &.closed {
    background-color: #f3f4f6;
    color: #374151;
  }

  &.false-positive {
    background-color: #fdf2f8;
    color: #be185d;
  }

  &.accepted-risk {
    background-color: #fef7ff;
    color: #a21caf;
  }

  &.duplicate {
    background-color: #f1f5f9;
    color: #475569;
  }
}

/* Severity Classes */
.severity-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  margin-right: 8px;
  margin-bottom: 4px;

  &.critical {
    background-color: #fef2f2;
    color: #dc2626;
    border: 1px solid #fecaca;
  }

  &.high {
    background-color: #fef3c7;
    color: #d97706;
    border: 1px solid #fed7aa;
  }

  &.medium {
    background-color: #fef7ff;
    color: #a21caf;
    border: 1px solid #f3e8ff;
  }

  &.low {
    background-color: #dbeafe;
    color: #1e40af;
    border: 1px solid #bfdbfe;
  }

  &.informational {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #d1d5db;
  }
}

.severity-info {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

/* Code Content Styling */
.code-content {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  background-color: #f8fafc;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  white-space: pre-wrap;
  word-break: break-all;
}

/* Vulnerability Counts Section */
.vulnerability-counts-section {
  margin-top: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #fef7ff, #f0f9ff);
  border: 1px solid #e0e7ff;
  border-radius: 8px;

  .section-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .counts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 16px;

    .form-group {
      margin-bottom: 0;

      label {
        display: block;
        font-weight: 500;
        color: #374151;
        margin-bottom: 6px;
        font-size: 13px;
      }

      .count-input {
        width: 100%;
        padding: 8px 12px;
        border: 2px solid;
        border-radius: 6px;
        font-size: 14px;
        font-weight: 600;
        text-align: center;
        background-color: white;

        &:focus {
          outline: none;
          box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
        }

        &.high {
          border-color: #ea580c;
          color: #ea580c;

          &:focus {
            box-shadow: 0 0 0 3px rgba(234, 88, 12, 0.2);
          }
        }

        &.medium {
          border-color: #d97706;
          color: #d97706;

          &:focus {
            box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.2);
          }
        }

        &.low {
          border-color: #059669;
          color: #059669;

          &:focus {
            box-shadow: 0 0 0 3px rgba(5, 150, 105, 0.2);
          }
        }

        &.informational {
          border-color: #0284c7;
          color: #0284c7;

          &:focus {
            box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.2);
          }
        }

        &.false-positive {
          border-color: #6b7280;
          color: #6b7280;

          &:focus {
            box-shadow: 0 0 0 3px rgba(107, 114, 128, 0.2);
          }
        }
      }
    }
  }
}

/* Mini Vulnerability Summary for View Mode */
.mini-vuln-summary {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 8px;

  .mini-count {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 10px;
    font-size: 10px;
    font-weight: 600;

    &.high {
      background-color: #fff7ed;
      color: #ea580c;
    }

    &.medium {
      background-color: #fef3c7;
      color: #d97706;
    }

    &.low {
      background-color: #ecfdf5;
      color: #059669;
    }

    &.info {
      background-color: #eff6ff;
      color: #0284c7;
    }

    &.false-positive {
      background-color: #f3f4f6;
      color: #6b7280;
    }
  }
}

.view-report-button.small {
  background-color: #3b82f6;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
  font-weight: 500;
  transition: background-color 0.2s;

  &:hover {
    background-color: #2563eb;
  }
}

.security-report-button {
  background-color: #f59e0b;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: background-color 0.2s;

  &:hover {
    background-color: #d97706;
  }
}

/* Security Report Modal */
.security-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
}

.security-modal-content {
  background-color: white;
  border-radius: 12px;
  width: 95%;
  max-width: 1200px;
  height: 90%;
  max-height: 800px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.security-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  background-color: #f8fafc;
  border-radius: 12px 12px 0 0;

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }

  .security-modal-close-button {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }
}

.security-modal-body {
  flex: 1;
  padding: 0;
  overflow: auto;

  .security-report-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: #6b7280;

    .loading-spinner {
      width: 32px;
      height: 32px;
      border: 3px solid #e5e7eb;
      border-top: 3px solid #3b82f6;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 16px;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  }

  .security-report-error {
    padding: 24px;
    text-align: center;
    color: #dc2626;
    background-color: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 8px;
    margin: 16px;
  }

  .security-report-empty {
    padding: 24px;
    text-align: center;
    color: #6b7280;
  }

  .security-report-content {
    padding: 16px;

    /* Style the injected HTML content */
    :deep(h1), :deep(h2), :deep(h3) {
      color: #1e293b;
      margin-top: 24px;
      margin-bottom: 12px;
    }

    :deep(table) {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;
    }

    :deep(th), :deep(td) {
      padding: 8px 12px;
      border: 1px solid #e5e7eb;
      text-align: left;
    }

    :deep(th) {
      background-color: #f8fafc;
      font-weight: 600;
    }

    :deep(.high) { color: #dc2626; }
    :deep(.medium) { color: #ea580c; }
    :deep(.low) { color: #ca8a04; }
    :deep(.informational) { color: #2563eb; }
  }
}

/* Modal Layout */
.modal-layout {
  display: grid;
  height: 100%;
  gap: 1px;
  background-color: #e5e7eb;

  &.two-column {
    grid-template-columns: 1fr 2fr;
  }

  &.three-column {
    grid-template-columns: 1fr 2fr 1fr;
  }
}

.test-case-details-column,
.history-column,
.update-result-column {
  background-color: white;
  overflow-y: auto;
  padding: 20px;
}

.test-case-details-column {
  border-right: 1px solid #e5e7eb;
}

.update-result-column {
  border-left: 1px solid #e5e7eb;
}

/* Test Case Details */
.test-case-info {
  .test-case-title {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 16px;
    line-height: 1.4;
  }

  .test-case-properties {
    .test-case-property {
      margin-bottom: 16px;

      strong {
        display: block;
        font-weight: 500;
        color: #374151;
        margin-bottom: 4px;
        font-size: 13px;
      }

      p {
        color: #6b7280;
        font-size: 13px;
        line-height: 1.5;
        margin: 0;
      }

      span {
        color: #6b7280;
        font-size: 13px;
      }

      .tag-badge {
        display: inline-block;
        background-color: #e0f2f7;
        color: #0366d6;
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 11px;
        margin-right: 4px;
        margin-bottom: 2px;
      }
    }
  }
}

/* History Section */
.previous-changes-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;

  .history-section-title h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }

  .history-header-right {
    display: flex;
    align-items: center;
    gap: 12px;

    .history-count {
      font-size: 12px;
      color: #6b7280;
      background-color: #f3f4f6;
      padding: 4px 8px;
      border-radius: 12px;
    }

    .toggle-update-button {
      background-color: #f59e0b;
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      display: flex;
      align-items: center;
      gap: 6px;
      transition: background-color 0.2s;

      &:hover {
        background-color: #d97706;
      }

      &.active {
        background-color: #dc2626;

        &:hover {
          background-color: #b91c1c;
        }
      }
    }
  }
}

.empty-state {
  text-align: center;
  color: #6b7280;
  font-style: italic;
  padding: 40px 20px;
}

/* History Items */
.history-items-list {
  .history-item {
    margin-bottom: 16px;
    border-radius: 8px;
    border: 1px solid #e5e7eb;
    overflow: hidden;
    transition: all 0.2s;

    &:hover {
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    }

    &.current-item {
      border-color: #f59e0b;
      box-shadow: 0 0 0 1px #f59e0b;
    }

    .history-item-content {
      display: flex;
      cursor: pointer;
      transition: all 0.2s ease;
      border-radius: 8px;

      &:hover {
        background-color: #f8fafc;
        box-shadow: 0 2px 8px rgba(59, 130, 246, 0.1);
        transform: translateY(-1px);
      }

      .status-indicator {
        width: 4px;
        background-color: #e5e7eb;

        &.new { background-color: #3b82f6; }
        &.open { background-color: #f59e0b; }
        &.in-progress { background-color: #06b6d4; }
        &.fixed { background-color: #10b981; }
        &.verified { background-color: #059669; }
        &.closed { background-color: #6b7280; }
        &.false-positive { background-color: #ec4899; }
        &.accepted-risk { background-color: #a855f7; }
        &.duplicate { background-color: #64748b; }
      }

      .history-item-main {
        flex: 1;
        padding: 16px;

        .history-header {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;

          .status-badge {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 11px;
            font-weight: 500;
            text-transform: capitalize;
          }

          .timestamp {
            font-size: 12px;
            color: #6b7280;
          }

          .created-by {
            font-size: 11px;
            color: #9ca3af;
          }
        }

        .history-details {
          .detail-group {
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }

            label {
              display: block;
              font-weight: 500;
              color: #374151;
              font-size: 12px;
              margin-bottom: 4px;
            }

            .content-container {
              .truncated-content {
                color: #6b7280;
                font-size: 12px;
                line-height: 1.4;
                margin: 0;
                max-height: 60px;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 3;
                line-clamp: 3;
                -webkit-box-orient: vertical;

                &.empty-content {
                  color: #9ca3af;
                  font-style: italic;
                }
              }

              .show-more-button {
                background: none;
                border: none;
                color: #3b82f6;
                font-size: 11px;
                cursor: pointer;
                margin-top: 4px;
                text-decoration: underline;

                &:hover {
                  color: #2563eb;
                }
              }
            }
          }
        }
      }
    }
  }
}

/* Pagination */
.pagination-container {
  margin-top: 20px;

  &.fixed-pagination {
    position: sticky;
    bottom: 0;
    background-color: white;
    padding: 12px 0;
    border-top: 1px solid #e5e7eb;
  }

  .pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;

    .pagination-button {
      background-color: white;
      border: 1px solid #d1d5db;
      color: #374151;
      padding: 6px 10px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      transition: all 0.2s;

      &:hover:not(:disabled) {
        background-color: #f3f4f6;
        border-color: #9ca3af;
      }

      &:disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }
    }

    .pagination-pages {
      display: flex;
      gap: 2px;

      .pagination-page-button {
        background-color: white;
        border: 1px solid #d1d5db;
        color: #374151;
        padding: 6px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        min-width: 32px;
        text-align: center;
        transition: all 0.2s;

        &:hover {
          background-color: #f3f4f6;
          border-color: #9ca3af;
        }

        &.active {
          background-color: #f59e0b;
          color: white;
          border-color: #f59e0b;
        }
      }

      .pagination-ellipsis {
        padding: 6px 4px;
        color: #6b7280;
        font-size: 12px;
      }
    }
  }
}

/* Update Result Column */
.current-result-header {
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e5e7eb;

  h4 {
    font-size: 16px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }
}

.current-result {
  .result-view-mode {
    .result-card {
      .result-row {
        display: flex;
        margin-bottom: 16px;
        align-items: flex-start;

        .result-label {
          font-weight: 500;
          color: #374151;
          font-size: 13px;
          min-width: 120px;
          margin-right: 12px;
        }

        .result-value {
          flex: 1;
          font-size: 13px;
          color: #6b7280;

          &.timestamp {
            font-size: 12px;
          }

          &.result-text {
            line-height: 1.4;
            max-height: 80px;
            overflow-y: auto;
          }
        }
      }
    }
  }

  .result-edit-mode {
    .edit-form {
      .form-group {
        margin-bottom: 16px;

        label {
          display: block;
          font-weight: 500;
          color: #374151;
          margin-bottom: 6px;
          font-size: 13px;
        }

        .status-select,
        .severity-select {
          width: 100%;
          padding: 8px 10px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 13px;
          background-color: white;

          &:focus {
            outline: none;
            border-color: #f59e0b;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
          }

          &.status-warning-border {
            border-color: #f59e0b;
          }
        }

        .edit-textarea {
          width: 100%;
          min-height: 60px;
          padding: 8px 10px;
          border: 1px solid #d1d5db;
          border-radius: 6px;
          font-size: 13px;
          font-family: inherit;
          resize: vertical;

          &.large-textarea {
            min-height: 100px;
          }

          &:focus {
            outline: none;
            border-color: #f59e0b;
            box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
          }
        }
      }

      .edit-actions {
        display: flex;
        gap: 8px;
        margin-top: 20px;

        .save-button {
          background-color: #f59e0b;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 13px;
          font-weight: 500;
          display: flex;
          align-items: center;
          gap: 6px;
          transition: background-color 0.2s;

          &:hover:not(.disabled-button) {
            background-color: #d97706;
          }

          &.disabled-button {
            background-color: #9ca3af;
            cursor: not-allowed;
          }
        }

        .cancel-button {
          background-color: #6b7280;
          color: white;
          border: none;
          padding: 8px 16px;
          border-radius: 6px;
          cursor: pointer;
          font-size: 13px;
          font-weight: 500;
          transition: background-color 0.2s;

          &:hover {
            background-color: #4b5563;
          }
        }
      }
    }
  }
}

/* Text Modal */
.text-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1001;
}

.text-modal-content {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 80%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.text-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e5e7eb;

  h3 {
    font-size: 18px;
    font-weight: 600;
    color: #1e293b;
    margin: 0;
  }

  .text-modal-close-button {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #6b7280;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background-color: #f3f4f6;
      color: #374151;
    }
  }
}

.text-modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 20px;

  .text-modal-text {
    font-family: inherit;
    font-size: 14px;
    line-height: 1.5;
    color: #374151;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0;
  }
}
</style>
