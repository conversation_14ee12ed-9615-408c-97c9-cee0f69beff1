<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import axios from 'axios';
import TestRunDetail from './TestRunDetail.vue';
import TestRunDastDetail from './TestRunDastDetail.vue';

interface TestRun {
  id: string;
  name: string;
  description: string;
  startTime: string | null;
  endTime: string | null;
  environment: string | null;
  build: string | null;
  release: string | null;
  type: string;
}

const route = useRoute();
const projectId = route.params.id as string;
const testRunId = route.params.testRunId as string;

const testRun = ref<TestRun | null>(null);
const loading = ref(true);
const error = ref('');

const fetchTestRun = async () => {
  try {
    loading.value = true;
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}`
    );
    testRun.value = response.data;
  } catch (err: any) {
    error.value = err.response?.data?.message || 'Failed to fetch test run';
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchTestRun();
});
</script>

<template>
  <div class="test-run-detail-wrapper">
    <!-- Loading state -->
    <div v-if="loading" class="loading-container">
      <div class="loading-spinner"></div>
      <p>Loading test run details...</p>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="error-container">
      <div class="error-message">
        {{ error }}
      </div>
    </div>

    <!-- Render appropriate detail component based on test run type -->
    <TestRunDastDetail v-else-if="testRun && testRun.type === 'security-dast'" />
    <TestRunDetail v-else-if="testRun" />
  </div>
</template>

<style lang="scss" scoped>
.test-run-detail-wrapper {
  min-height: calc(100vh - 120px);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  gap: 16px;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f4f6;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }

  p {
    color: #6b7280;
    font-size: 16px;
  }
}

.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .error-message {
    background-color: #fef2f2;
    color: #b91c1c;
    padding: 16px 24px;
    border-radius: 8px;
    border: 1px solid #ef4444;
    font-size: 16px;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
