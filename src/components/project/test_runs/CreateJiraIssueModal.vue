<script setup lang="ts">
import { ref, watch, onMounted } from 'vue';
import axios from 'axios';

interface JiraProject {
  id: string;
  key: string;
  name: string;
}

interface JiraIssueType {
  id: string;
  name: string;
  iconUrl?: string;
}

interface JiraPriority {
  id: string;
  name: string;
  iconUrl?: string;
}

interface Props {
  show: boolean;
  testCaseTitle: string;
  testCaseSteps: string;
  testCaseExpectation: string;
  testCaseActualResult: string | null;
  testCaseNotes: string | null;
  testCaseScreenshotUrl: string | null;
  testCaseVideoUrl: string | null;
  projectId: string;
  testRunId: string;
  testResultId: string;
}

const props = defineProps<Props>();

const emit = defineEmits<{
  'close': [];
  'created': [issueKey: string];
}>();

// Form data
const summary = ref(props.testCaseTitle);
const description = ref('');
const selectedProject = ref('');
const selectedIssueType = ref('');
const selectedPriority = ref('');
const attachments = ref<File[]>([]);
const projectSearchQuery = ref('');
const filteredProjects = ref<JiraProject[]>([]);
const customFieldValues = ref<{ [key: string]: any }>({});
const customFieldSearchQuery = ref('');
const filteredCustomFields = ref<any[]>([]);
const selectedCustomFields = ref<string[]>([]);
const loadingFieldValues = ref<{ [key: string]: boolean }>({});
const debugMode = ref(false); // Set to true to see field debugging info
const dragOver = ref(false); // For drag and drop UI state

// Loading states
const loading = ref(false);
const projectsLoading = ref(false);
const issueTypesLoading = ref(false);
const prioritiesLoading = ref(false);
const customFieldsLoading = ref(false);

// Data lists
const projects = ref<JiraProject[]>([]);
const issueTypes = ref<JiraIssueType[]>([]);
const priorities = ref<JiraPriority[]>([]);
const customFields = ref<any[]>([]);

// Error handling
const error = ref('');

// Format description with test case details
const formatDescription = () => {
  const sections = [
    '**Steps to Reproduce:**\n' + props.testCaseSteps,
    '**Expected Result:**\n' + props.testCaseExpectation,
    '**Actual Result:**\n' + (props.testCaseActualResult || 'N/A'),
    '**Notes:**\n' + (props.testCaseNotes || 'N/A')
  ];

  if (props.testCaseScreenshotUrl) {
    sections.push(`**Screenshot:** [View](${props.testCaseScreenshotUrl})`);
  }

  if (props.testCaseVideoUrl) {
    sections.push(`**Video Recording:** [View](${props.testCaseVideoUrl})`);
  }

  return sections.join('\n\n');
};

// Initialize description when modal opens
const initializeDescription = () => {
  description.value = formatDescription();
};

// Fetch JIRA projects
const fetchProjects = async () => {
  try {
    projectsLoading.value = true;
    error.value = '';

    console.log('Fetching Jira projects...');

    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/integrations/jira/projects`
    );

    console.log('Projects response:', response.data);
    projects.value = response.data;

    // Initialize filtered projects
    filteredProjects.value = projects.value;
  } catch (err: any) {
    console.error('Failed to fetch JIRA projects:', err);
    console.error('Error details:', err.response || err.message);
    error.value = err.response?.data?.message || 'Failed to fetch JIRA projects';

    // Fallback to empty array
    projects.value = [];
    filteredProjects.value = [];
  } finally {
    projectsLoading.value = false;
  }
};

// Fetch issue types for selected project
const fetchIssueTypes = async () => {
  if (!selectedProject.value) return;

  try {
    issueTypesLoading.value = true;
    error.value = '';

    console.log(`Fetching issue types for project ID: ${selectedProject.value}`);

    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/integrations/jira/projects/${selectedProject.value}/issuetypes`
    );

    console.log('Issue types response:', response.data);
    issueTypes.value = response.data;
  } catch (err: any) {
    console.error('Failed to fetch issue types:', err);
    console.error('Error details:', err.response || err.message);
    error.value = err.response?.data?.message || 'Failed to fetch issue types';

    // Fallback to empty array
    issueTypes.value = [];
  } finally {
    issueTypesLoading.value = false;
  }
};

// Fetch priorities
const fetchPriorities = async (_projectId?: string) => {
  try {
    prioritiesLoading.value = true;
    error.value = '';

    // Always use the global priorities endpoint for now
    // This is a fallback until the project-specific endpoint is implemented
    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/integrations/jira/priorities`
    );

    priorities.value = response.data;

    // Log for debugging
    console.log(`Fetched ${priorities.value.length} priorities`);
  } catch (err: any) {
    console.error('Failed to fetch priorities:', err);
    error.value = err.response?.data?.message || 'Failed to fetch priorities';
  } finally {
    prioritiesLoading.value = false;
  }
};

// Handle file selection from input
const handleFileSelect = (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files) {
    // Add new files to existing attachments
    const newFiles = Array.from(input.files);
    attachments.value = [...attachments.value, ...newFiles];
  }
};

// Handle file drop for drag and drop
const handleFileDrop = (event: DragEvent) => {
  dragOver.value = false;
  if (event.dataTransfer?.files) {
    // Add dropped files to existing attachments
    const droppedFiles = Array.from(event.dataTransfer.files);
    attachments.value = [...attachments.value, ...droppedFiles];
  }
};

// Remove attachment
const removeAttachment = (index: number) => {
  attachments.value.splice(index, 1);
};

// File type detection helpers
const isImageFile = (file: File): boolean => {
  return file.type.startsWith('image/');
};

const isVideoFile = (file: File): boolean => {
  return file.type.startsWith('video/');
};

const isPdfFile = (file: File): boolean => {
  return file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf');
};

const isDocFile = (file: File): boolean => {
  const docTypes = ['application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
  return docTypes.includes(file.type) ||
         file.name.toLowerCase().endsWith('.doc') ||
         file.name.toLowerCase().endsWith('.docx');
};

const isSpreadsheetFile = (file: File): boolean => {
  const spreadsheetTypes = [
    'application/vnd.ms-excel',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
  ];
  return spreadsheetTypes.includes(file.type) ||
         file.name.toLowerCase().endsWith('.xls') ||
         file.name.toLowerCase().endsWith('.xlsx');
};

// Format file size for display
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';

  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Create JIRA issue
const createIssue = async () => {
  if (!summary.value || !selectedProject.value || !selectedIssueType.value || !selectedPriority.value) {
    error.value = 'Please fill in all required fields';
    return;
  }

  try {
    loading.value = true;
    error.value = '';

    // Create FormData for file uploads
    const formData = new FormData();
    formData.append('summary', summary.value);
    formData.append('description', description.value);
    formData.append('projectId', selectedProject.value);
    formData.append('issueType', selectedIssueType.value);
    formData.append('priority', selectedPriority.value);
    formData.append('testResultId', props.testResultId);

    // Add custom field values
    for (const [fieldId, value] of Object.entries(customFieldValues.value)) {
      if (value !== null && value !== undefined && value !== '') {
        formData.append(`customFields[${fieldId}]`, value.toString());
      }
    }

    // Track if we have attachments
    const hasAttachments = attachments.value.length > 0;

    // Add attachments
    attachments.value.forEach(file => {
      formData.append('attachments', file);
    });

    const response = await axios.post(
      `${(import.meta as any).env.VITE_BACKEND_URL}/projects/${props.projectId}/test-runs/${props.testRunId}/test-results/${props.testResultId}/jira-issue`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      }
    );

    // Show a success message if there are attachments
    if (hasAttachments) {
      // Use a custom event to show a toast notification
      const fileCount = attachments.value.length;
      const fileText = fileCount === 1 ? 'attachment' : 'attachments';
      const event = new CustomEvent('show-toast', {
        detail: {
          message: `JIRA issue ${response.data.key} created successfully with ${fileCount} ${fileText}. Attachments are being processed and may take a moment to appear in JIRA.`,
          type: 'success',
          duration: 8000 // Show for 8 seconds
        }
      });
      window.dispatchEvent(event);
    }

    emit('created', response.data.key);
    emit('close');
  } catch (err: any) {
    console.error('Failed to create JIRA issue:', err);
    error.value = err.response?.data?.message || 'Failed to create JIRA issue';
  } finally {
    loading.value = false;
  }
};

// Filter projects based on search query
const filterProjects = () => {
  if (!projectSearchQuery.value) {
    filteredProjects.value = projects.value;
    return;
  }

  const query = projectSearchQuery.value.toLowerCase();
  filteredProjects.value = projects.value.filter(project =>
    project.name.toLowerCase().includes(query) ||
    project.key.toLowerCase().includes(query)
  );
};

// Watch for changes in the project search query
watch(projectSearchQuery, filterProjects);

// Watch for changes in the projects list
watch(projects, filterProjects);

// Filter custom fields based on search query
const filterCustomFields = () => {
  if (!customFieldSearchQuery.value) {
    filteredCustomFields.value = customFields.value;
    return;
  }

  const query = customFieldSearchQuery.value.toLowerCase();
  filteredCustomFields.value = customFields.value.filter(field =>
    field.name.toLowerCase().includes(query) ||
    field.id.toLowerCase().includes(query)
  );
};

// Watch for changes in the custom field search query
watch(customFieldSearchQuery, filterCustomFields);

// Watch for changes in the custom fields list
watch(customFields, filterCustomFields);

// Fetch allowed values for a specific field
const fetchFieldValues = async (fieldId: string) => {
  if (!selectedProject.value) return;

  try {
    loadingFieldValues.value[fieldId] = true;

    console.log(`Fetching values for field ${fieldId}...`);

    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/integrations/jira/projects/${selectedProject.value}/customfields/${fieldId}/values`
    );

    console.log(`Received values for field ${fieldId}:`, response.data);

    // Find the field in the custom fields array
    const fieldIndex = customFields.value.findIndex(f => f.id === fieldId);
    if (fieldIndex >= 0) {
      // Update the field's allowed values
      customFields.value[fieldIndex].allowedValues = response.data;

      // Also update in filtered fields if present
      const filteredIndex = filteredCustomFields.value.findIndex(f => f.id === fieldId);
      if (filteredIndex >= 0) {
        filteredCustomFields.value[filteredIndex].allowedValues = response.data;
      }
    }
  } catch (err: any) {
    console.error(`Failed to fetch values for field ${fieldId}:`, err);
  } finally {
    loadingFieldValues.value[fieldId] = false;
  }
};

// Toggle selection of a custom field
const toggleCustomField = (fieldId: string) => {
  const index = selectedCustomFields.value.indexOf(fieldId);
  if (index === -1) {
    // Add to selected fields
    selectedCustomFields.value.push(fieldId);

    // If it's a dropdown field, fetch its values
    const field = customFields.value.find(f => f.id === fieldId);
    if (field && (field.isSelectField || isDropdownField(field)) &&
        (!field.allowedValues || field.allowedValues.length === 0)) {
      fetchFieldValues(fieldId);
    }
  } else {
    // Remove from selected fields
    selectedCustomFields.value.splice(index, 1);

    // Clear the value
    delete customFieldValues.value[fieldId];
  }
};

// Helper function to determine if a field should be rendered as a dropdown
const isDropdownField = (field: any): boolean => {
  // Known dropdown field IDs
  const knownDropdownFields = [
    'customfield_11508', // Bug Category
    'customfield_11536', // Severity
    'customfield_11510'  // Add other known dropdown fields here
  ];

  // Check if it's a known dropdown field
  if (knownDropdownFields.includes(field.id)) {
    return true;
  }

  // Check if it has allowed values
  if (field.allowedValues && field.allowedValues.length > 0) {
    return true;
  }

  // Check field type
  if (field.type === 'option' || field.type === 'array') {
    return true;
  }

  // Check schema
  if (field.schema) {
    if (field.schema.type === 'option' || field.schema.type === 'array') {
      return true;
    }

    if (field.schema.custom && (
      field.schema.custom.includes('select') ||
      field.schema.custom.includes('option') ||
      field.schema.custom.includes('dropdown')
    )) {
      return true;
    }
  }

  return false;
};

// Initialize data when component mounts
watch(() => props.show, (newValue) => {
  if (newValue) {
    initialize();
  }
});

// Also initialize when component is mounted if modal is already showing
onMounted(() => {
  if (props.show) {
    initialize();
  }
});

const initialize = async () => {
  initializeDescription();

  try {
    // Only fetch projects initially, priorities will be fetched after project selection
    await fetchProjects();

    // If no projects were loaded, show a helpful error message
    if (projects.value.length === 0) {
      error.value = 'No Jira projects found. Please check your Jira integration settings.';
    }
  } catch (err) {
    console.error('Error during initialization:', err);
    error.value = 'Failed to initialize Jira integration. Please try again later.';
  }
};

// Fetch custom fields for selected project
const fetchCustomFields = async () => {
  if (!selectedProject.value) return;

  try {
    customFieldsLoading.value = true;
    error.value = '';

    console.log(`Fetching custom fields for project ID: ${selectedProject.value}`);

    const response = await axios.get(
      `${(import.meta as any).env.VITE_BACKEND_URL}/integrations/jira/projects/${selectedProject.value}/customfields`
    );

    console.log('Custom fields response:', response.data);
    customFields.value = response.data;

    // Initialize filtered custom fields
    filteredCustomFields.value = customFields.value;

    // Reset custom field values and search query
    customFieldValues.value = {};
    customFieldSearchQuery.value = '';
  } catch (err: any) {
    console.error('Failed to fetch custom fields:', err);
    console.error('Error details:', err.response || err.message);
    error.value = err.response?.data?.message || 'Failed to fetch custom fields';

    // Fallback to empty array
    customFields.value = [];
    filteredCustomFields.value = [];
  } finally {
    customFieldsLoading.value = false;
  }
};

// Watch for project selection to fetch issue types, priorities, and custom fields
watch(selectedProject, () => {
  // Reset dependent fields
  selectedIssueType.value = '';
  selectedPriority.value = '';
  customFieldValues.value = {};

  if (selectedProject.value) {
    // Fetch data for the selected project
    fetchIssueTypes();
    fetchPriorities(selectedProject.value);
    fetchCustomFields();
  }
});
</script>

<template>
  <div v-if="show" class="modal-overlay">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Create JIRA Issue</h3>
        <button class="close-button" @click="emit('close')">×</button>
      </div>

      <div class="modal-body">
        <div v-if="error" class="error-message">
          {{ error }}
        </div>

        <form @submit.prevent="createIssue" class="two-column-form">
          <div class="form-column">
            <!-- Left Column - Standard Fields -->

            <!-- Summary -->
            <div class="form-group">
              <label>Summary *</label>
              <input
                v-model="summary"
                type="text"
                required
                placeholder="Enter issue summary"
              />
            </div>

            <!-- Project -->
            <div class="form-group">
              <label>Project *</label>
              <div class="search-container">
                <input
                  type="text"
                  v-model="projectSearchQuery"
                  placeholder="Search for a project..."
                  :disabled="projectsLoading"
                  class="search-input"
                />
                <div v-if="projectSearchQuery && filteredProjects.length > 0" class="search-results">
                  <div
                    v-for="project in filteredProjects"
                    :key="project.id"
                    class="search-result-item"
                    @click="selectedProject = project.id; projectSearchQuery = project.name + ' (' + project.key + ')'"
                  >
                    {{ project.name }} ({{ project.key }})
                  </div>
                </div>
              </div>
              <div v-if="selectedProject" class="selected-project">
                Selected: {{ projects.find(p => p.id === selectedProject)?.name }}
                ({{ projects.find(p => p.id === selectedProject)?.key }})
              </div>
              <div v-if="projectsLoading" class="loading-text">
                Loading projects...
              </div>
            </div>

            <!-- Issue Type -->
            <div class="form-group">
              <label>Issue Type *</label>
              <select
                v-model="selectedIssueType"
                required
                :disabled="!selectedProject || issueTypesLoading"
              >
                <option value="">Select an issue type</option>
                <option
                  v-for="type in issueTypes"
                  :key="type.id"
                  :value="type.id"
                >
                  {{ type.name }}
                </option>
              </select>
              <div v-if="issueTypesLoading" class="loading-text">
                Loading issue types...
              </div>
            </div>

            <!-- Priority -->
            <div class="form-group">
              <label>Priority *</label>
              <select
                v-model="selectedPriority"
                required
                :disabled="prioritiesLoading || !selectedProject"
              >
                <option value="">Select priority</option>
                <option
                  v-for="priority in priorities"
                  :key="priority.id"
                  :value="priority.id"
                >
                  {{ priority.name }}
                </option>
              </select>
              <div v-if="prioritiesLoading" class="loading-text">
                Loading priorities...
              </div>
            </div>

            <!-- Description -->
            <div class="form-group">
              <label>Description</label>
              <textarea
                v-model="description"
                rows="10"
                placeholder="Enter issue description"
              ></textarea>
            </div>

            <!-- Attachments -->
            <div class="form-group">
              <label>Attachments <span class="file-count" v-if="attachments.length > 0">({{ attachments.length }} {{ attachments.length === 1 ? 'file' : 'files' }} selected)</span></label>
              <div class="file-upload-container">
                <div class="file-drop-area"
                     @dragover.prevent="dragOver = true"
                     @dragleave.prevent="dragOver = false"
                     @drop.prevent="handleFileDrop"
                     :class="{ 'drag-over': dragOver }">
                  <div class="file-drop-message">
                    <div class="upload-icon">
                      <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                        <polyline points="17 8 12 3 7 8"></polyline>
                        <line x1="12" y1="3" x2="12" y2="15"></line>
                      </svg>
                    </div>
                    <p>Drag and drop files here or</p>
                    <label class="browse-button">
                      Browse Files
                      <input
                        type="file"
                        multiple
                        @change="handleFileSelect"
                        accept="image/*,video/*,.pdf,.doc,.docx,.xls,.xlsx"
                        class="hidden-file-input"
                      />
                    </label>
                    <p class="file-types">Supported file types: Images, Videos, PDF, DOC, DOCX, XLS, XLSX</p>
                  </div>
                </div>
              </div>
              <div v-if="attachments.length > 0" class="attachments-list">
                <div
                  v-for="(file, index) in attachments"
                  :key="index"
                  class="attachment-item"
                >
                  <div class="file-icon">
                    <span v-if="isImageFile(file)">🖼️</span>
                    <span v-else-if="isVideoFile(file)">🎬</span>
                    <span v-else-if="isPdfFile(file)">📄</span>
                    <span v-else-if="isDocFile(file)">📝</span>
                    <span v-else-if="isSpreadsheetFile(file)">📊</span>
                    <span v-else>📎</span>
                  </div>
                  <div class="file-info">
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">{{ formatFileSize(file.size) }}</span>
                  </div>
                  <button
                    type="button"
                    class="remove-button"
                    @click="removeAttachment(index)"
                    title="Remove file"
                  >
                    ×
                  </button>
                </div>
              </div>
              <div v-if="attachments.length > 0" class="attachment-info">
                <div class="info-icon">ℹ️</div>
                <div class="info-text">
                  <p><strong>Note about attachments:</strong></p>
                  <p>Attachments will be uploaded to JIRA, but may not appear immediately in the JIRA issue.</p>
                  <p>If you don't see attachments right away, please wait a few moments and refresh the JIRA page.</p>
                </div>
              </div>
            </div>
          </div>

          <div class="form-column">
            <!-- Right Column - Custom Fields -->
            <div class="custom-fields-header">
              <h4 class="custom-fields-title">Custom Fields</h4>
              <button
                type="button"
                class="debug-toggle"
                @click="debugMode = !debugMode"
                title="Toggle debug mode"
              >
                {{ debugMode ? 'Hide Debug' : 'Debug' }}
              </button>
            </div>

            <div v-if="customFieldsLoading" class="loading-text">
              Loading custom fields...
            </div>

            <div v-else-if="customFields.length === 0 && selectedProject" class="info-message">
              No custom fields available for this project.
            </div>

            <div v-else-if="!selectedProject" class="info-message">
              Select a project to see available custom fields.
            </div>

            <!-- Search field for custom fields -->
            <div v-else class="custom-field-search">
              <input
                type="text"
                v-model="customFieldSearchQuery"
                placeholder="Search for custom fields..."
                class="search-input"
              />
              <span v-if="filteredCustomFields.length < customFields.length" class="search-count">
                Showing {{ filteredCustomFields.length }} of {{ customFields.length }} fields
              </span>
            </div>

            <!-- No results message -->
            <div v-if="filteredCustomFields.length === 0 && customFieldSearchQuery" class="info-message">
              No custom fields match your search query.
            </div>

            <!-- Custom fields selection list -->
            <div class="custom-fields-list">
              <div v-for="field in filteredCustomFields" :key="field.id" class="custom-field-list-item">
                <div class="custom-field-header" @click="toggleCustomField(field.id)">
                  <div class="custom-field-checkbox">
                    <input
                      type="checkbox"
                      :id="`field-checkbox-${field.id}`"
                      :checked="selectedCustomFields.includes(field.id)"
                      @click.stop
                      @change="toggleCustomField(field.id)"
                    />
                    <label :for="`field-checkbox-${field.id}`">{{ field.name }}</label>
                  </div>
                  <div class="custom-field-type">
                    <span v-if="isDropdownField(field)" class="field-type-badge dropdown-badge">Dropdown</span>
                    <span v-else-if="field.type === 'string'" class="field-type-badge text-badge">Text</span>
                    <span v-else-if="field.type === 'number'" class="field-type-badge number-badge">Number</span>
                    <span v-else-if="field.type === 'date'" class="field-type-badge date-badge">Date</span>
                    <span v-else class="field-type-badge">{{ field.type }}</span>
                  </div>
                </div>

                <!-- Field input (only shown when selected) -->
                <div v-if="selectedCustomFields.includes(field.id)" class="custom-field-input">
                  <!-- Debug info for development -->
                  <div v-if="debugMode" class="field-debug">
                    ID: {{ field.id }}<br>
                    Type: {{ field.type }}<br>
                    Required: {{ field.required }}<br>
                    Schema: {{ JSON.stringify(field.schema) }}<br>
                    Allowed Values: {{ field.allowedValues?.length || 0 }} items
                  </div>

                  <!-- Loading indicator -->
                  <div v-if="loadingFieldValues[field.id]" class="loading-field-values">
                    Loading field values...
                  </div>

                  <!-- Render the appropriate input based on field type -->
                  <div v-else class="form-group custom-field-item">
                    <label>
                      {{ field.name }}
                      <span v-if="field.required" class="required-marker">*</span>
                    </label>

                    <!-- Text field -->
                    <div v-if="field.type === 'string' && !field.allowedValues?.length" class="text-field-container">
                      <input
                        v-model="customFieldValues[field.id]"
                        type="text"
                        :required="field.required"
                        :placeholder="`Enter ${field.name}`"
                      />
                      <div class="field-type-indicator text-indicator" title="Text field">
                        <span>Aa</span>
                      </div>
                    </div>

                    <!-- Number field -->
                    <div v-else-if="field.type === 'number'" class="number-field-container">
                      <input
                        v-model="customFieldValues[field.id]"
                        type="number"
                        :required="field.required"
                        :placeholder="`Enter ${field.name}`"
                      />
                      <div class="field-type-indicator number-indicator" title="Number field">
                        <span>123</span>
                      </div>
                    </div>

                    <!-- Date field -->
                    <div v-else-if="field.type === 'date' || field.type === 'datetime'" class="date-field-container">
                      <input
                        v-model="customFieldValues[field.id]"
                        type="date"
                        :required="field.required"
                      />
                      <div class="field-type-indicator date-indicator" title="Date field">
                        <span>📅</span>
                      </div>
                    </div>

                    <!-- Select field (dropdown) -->
                    <div v-else-if="isDropdownField(field)" class="select-field-container">
                      <select
                        v-model="customFieldValues[field.id]"
                        :required="field.required"
                        class="dropdown-field"
                      >
                        <option value="">Select {{ field.name }}</option>

                        <!-- If we have allowed values, show them -->
                        <template v-if="field.allowedValues && field.allowedValues.length > 0">
                          <option
                            v-for="option in field.allowedValues"
                            :key="option.id || option.value"
                            :value="option.id || option.value"
                          >
                            {{ option.value || option.name || option.id }}
                          </option>
                        </template>
                      </select>
                      <div class="field-type-indicator dropdown-indicator" title="Dropdown field">
                        <span>▼</span>
                      </div>
                    </div>

                    <!-- Textarea for long text -->
                    <textarea
                      v-else-if="field.type === 'text' || field.type === 'paragraph'"
                      v-model="customFieldValues[field.id]"
                      rows="4"
                      :required="field.required"
                      :placeholder="`Enter ${field.name}`"
                    ></textarea>

                    <!-- Checkbox for boolean fields -->
                    <div v-else-if="field.type === 'boolean'" class="checkbox-field">
                      <input
                        :id="`field-${field.id}`"
                        type="checkbox"
                        v-model="customFieldValues[field.id]"
                        :required="field.required"
                      />
                      <label :for="`field-${field.id}`">{{ field.name }}</label>
                    </div>

                    <!-- Array/multi-select fields -->
                    <select
                      v-else-if="field.type === 'array'"
                      v-model="customFieldValues[field.id]"
                      multiple
                      :required="field.required"
                    >
                      <option
                        v-for="option in field.allowedValues || []"
                        :key="option.id || option.value"
                        :value="option.id || option.value"
                      >
                        {{ option.value || option.name || option.id }}
                      </option>
                    </select>

                    <!-- Default to text input if type is unknown -->
                    <input
                      v-else
                      v-model="customFieldValues[field.id]"
                      type="text"
                      :required="field.required"
                      :placeholder="`Enter ${field.name} (${field.type || 'unknown type'})`"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="form-actions full-width">
            <button
              type="button"
              class="cancel-button"
              @click="emit('close')"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="submit-button"
              :disabled="loading"
            >
              {{ loading ? 'Creating...' : 'Create Issue' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 95%;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 20px;
    color: #374151;
  }
}

.modal-body {
  padding: 24px;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  color: #6b7280;
  cursor: pointer;

  &:hover {
    color: #374151;
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 20px;

  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
  }

  input,
  select,
  textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }

    &:disabled {
      background-color: #f3f4f6;
      cursor: not-allowed;
    }
  }

  textarea {
    resize: vertical;
  }
}

.loading-text {
  font-size: 14px;
  color: #6b7280;
  margin-top: 4px;
}

.search-container {
  position: relative;
  width: 100%;
}

.search-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
}

.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-top: none;
  border-radius: 0 0 6px 6px;
  z-index: 10;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.search-result-item {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 14px;

  &:hover {
    background-color: #f3f4f6;
  }
}

.selected-project {
  margin-top: 8px;
  padding: 8px 12px;
  background-color: #f0f7ff;
  border-radius: 6px;
  border: 1px solid #cce5ff;
  font-size: 14px;
  color: #1e40af;
}

.file-count {
  font-size: 14px;
  color: #6b7280;
  font-weight: normal;
}

.file-upload-container {
  margin-bottom: 16px;
}

.file-drop-area {
  border: 2px dashed #e5e7eb;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  transition: all 0.2s ease;
  background-color: #f9fafb;
  cursor: pointer;

  &:hover {
    border-color: #d1d5db;
    background-color: #f3f4f6;
  }

  &.drag-over {
    border-color: #e94560;
    background-color: rgba(233, 69, 96, 0.05);
  }
}

.file-drop-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;

  p {
    margin: 0;
    color: #6b7280;
    font-size: 14px;
  }

  .file-types {
    font-size: 12px;
    color: #9ca3af;
  }
}

.upload-icon {
  color: #6b7280;
  margin-bottom: 8px;

  svg {
    width: 32px;
    height: 32px;
  }
}

.browse-button {
  background-color: #e94560;
  color: white;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #d63553;
  }
}

.hidden-file-input {
  display: none;
}

.attachments-list {
  margin-top: 16px;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  border-bottom: 1px solid #e5e7eb;
  transition: background-color 0.2s;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f9fafb;
  }

  .file-icon {
    font-size: 18px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
  }

  .file-info {
    flex: 1;
    display: flex;
    flex-direction: column;

    .file-name {
      font-size: 14px;
      color: #374151;
      margin-bottom: 2px;
      word-break: break-all;
    }

    .file-size {
      font-size: 12px;
      color: #6b7280;
    }
  }

  .remove-button {
    background: none;
    border: none;
    color: #ef4444;
    cursor: pointer;
    font-size: 18px;
    padding: 0 8px;
    margin-left: 8px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 28px;
    width: 28px;

    &:hover {
      color: #dc2626;
      background-color: #fee2e2;
    }
  }
}

.two-column-form {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
}

.form-column {
  flex: 1;
  min-width: 300px;
}

.custom-fields-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
  padding-bottom: 8px;
}

.custom-fields-title {
  margin: 0;
  color: #374151;
  font-size: 18px;
}

.debug-toggle {
  background-color: #f3f4f6;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  color: #6b7280;
  cursor: pointer;

  &:hover {
    background-color: #e5e7eb;
  }
}

.custom-field-search {
  margin-bottom: 16px;
  position: relative;

  .search-input {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 14px;

    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }

  .search-count {
    display: block;
    font-size: 12px;
    color: #6b7280;
    margin-top: 4px;
  }
}

.info-message {
  background-color: #f0f7ff;
  color: #1e40af;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 14px;
}

.required-marker {
  color: #ef4444;
  margin-left: 2px;
}

.full-width {
  width: 100%;
}

.checkbox-field {
  display: flex;
  align-items: center;
  gap: 8px;

  input[type="checkbox"] {
    width: auto;
    margin-right: 8px;
  }

  label {
    margin-bottom: 0;
    font-weight: normal;
  }
}

.field-debug {
  font-size: 10px;
  color: #6b7280;
  background-color: #f3f4f6;
  padding: 4px;
  border-radius: 4px;
  margin-bottom: 4px;
  font-family: monospace;
  white-space: pre-wrap;
}

select[multiple] {
  height: auto;
  min-height: 100px;
}

.custom-field-item {
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 16px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #d1d5db;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  label {
    font-weight: 600;
    color: #4b5563;
    margin-bottom: 10px;
  }
}

.text-field-container,
.number-field-container,
.date-field-container,
.select-field-container {
  position: relative;

  input, select {
    width: 100%;
    padding-right: 30px;
  }

  .field-type-indicator {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }
}

.select-field-container {
  .dropdown-field {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
  }
}

.text-indicator {
  background-color: #e0f2fe;
  color: #0369a1;
}

.number-indicator {
  background-color: #dcfce7;
  color: #166534;
}

.date-indicator {
  background-color: #fef3c7;
  color: #92400e;
}

.dropdown-indicator {
  background-color: #f3e8ff;
  color: #7e22ce;
}

.attachment-info {
  margin-top: 12px;
  padding: 12px;
  background-color: #fff8e6;
  border: 1px solid #fde68a;
  border-radius: 6px;
  display: flex;
  gap: 12px;

  .info-icon {
    font-size: 20px;
    line-height: 1;
  }

  .info-text {
    flex: 1;

    p {
      margin: 0 0 6px 0;
      font-size: 13px;
      line-height: 1.4;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        color: #92400e;
      }
    }
  }
}

.custom-fields-list {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.custom-field-list-item {
  border-bottom: 1px solid #e5e7eb;

  &:last-child {
    border-bottom: none;
  }
}

.custom-field-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #f9fafb;
  }
}

.custom-field-checkbox {
  display: flex;
  align-items: center;
  gap: 8px;

  input[type="checkbox"] {
    width: 18px;
    height: 18px;
    margin: 0;
  }

  label {
    margin: 0;
    font-weight: 500;
    cursor: pointer;
  }
}

.custom-field-type {
  display: flex;
  gap: 8px;
}

.field-type-badge {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: #f3f4f6;
  color: #6b7280;
}

.text-badge {
  background-color: #e0f2fe;
  color: #0369a1;
}

.number-badge {
  background-color: #dcfce7;
  color: #166534;
}

.date-badge {
  background-color: #fef3c7;
  color: #92400e;
}

.dropdown-badge {
  background-color: #f3e8ff;
  color: #7e22ce;
}

.custom-field-input {
  padding: 0 16px 16px;
  background-color: #f9fafb;
  border-top: 1px solid #e5e7eb;
}

.loading-field-values {
  padding: 12px;
  font-size: 14px;
  color: #6b7280;
  font-style: italic;
}

.field-type-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background-color: #f3f4f6;
  border-radius: 4px;
  padding: 2px 6px;
  margin-left: 6px;
  font-size: 12px;
  color: #6b7280;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 24px;

  button {
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }

  .cancel-button {
    background-color: #f3f4f6;
    color: #374151;
    border: 1px solid #e5e7eb;

    &:hover {
      background-color: #e5e7eb;
    }
  }

  .submit-button {
    background-color: #e94560;
    color: white;
    border: none;

    &:hover:not(:disabled) {
      background-color: #d63553;
    }
  }
}
</style>