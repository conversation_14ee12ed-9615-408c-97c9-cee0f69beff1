<script setup lang="ts">
import { computed } from 'vue';

interface TestRun {
  id: string;
  name: string;
  description: string;
  startTime: string | null;
  endTime: string | null;
  environment: string | null;
  build: string | null;
  release: string | null;
  selectionType: string;
  type: string;
  createdAt: string;
  updatedAt: string;
}

const props = defineProps<{
  testRuns: TestRun[];
  loading: boolean;
  selectedTestRuns: string[];
}>();

const emit = defineEmits<{
  'update:selectedTestRuns': [value: string[]];
  'view': [testRun: TestRun];
  'edit': [testRun: TestRun];
  'delete': [testRun: TestRun];
}>();

const allSelected = computed({
  get: () => props.testRuns.length > 0 && props.selectedTestRuns.length === props.testRuns.length,
  set: (value: boolean) => {
    emit('update:selectedTestRuns', value ? props.testRuns.map(tr => tr.id) : []);
  }
});

const toggleSelection = (testRunId: string) => {
  const selected = [...props.selectedTestRuns];
  const index = selected.indexOf(testRunId);
  
  if (index === -1) {
    selected.push(testRunId);
  } else {
    selected.splice(index, 1);
  }
  
  emit('update:selectedTestRuns', selected);
};
</script>

<template>
  <div class="test-runs-table">
    <table>
      <thead>
        <tr>
          <th class="checkbox-column">
            <input
              type="checkbox"
              :checked="allSelected"
              @change="e => allSelected = (e.target as HTMLInputElement).checked"
            />
          </th>
          <th>Name</th>
          <th>Type</th>
          <th>Environment</th>
          <th>Build</th>
          <th>Release</th>
          <th>Created</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        <template v-if="loading">
          <tr>
            <td colspan="8" class="loading-cell">
              Loading test runs...
            </td>
          </tr>
        </template>
        <template v-else-if="testRuns.length === 0">
          <tr>
            <td colspan="8" class="empty-cell">
              No test runs found.
            </td>
          </tr>
        </template>
        <template v-else>
          <tr v-for="testRun in testRuns" :key="testRun.id" class="test-run-row" @click="emit('view', testRun)">
            <td class="checkbox-column" @click.stop>
              <input
                type="checkbox"
                :checked="selectedTestRuns.includes(testRun.id)"
                @change="() => toggleSelection(testRun.id)"
              />
            </td>
            <td>
              {{ testRun.name }}
            </td>
            <td>
              <span :class="['type-badge', testRun.type === 'security-dast' ? 'security' : 'general']">
                {{ testRun.type === 'security-dast' ? 'Security DAST' : 'General' }}
              </span>
            </td>
            <td>{{ testRun.environment || '-' }}</td>
            <td>{{ testRun.build || '-' }}</td>
            <td>{{ testRun.release || '-' }}</td>
            <td>{{ new Date(testRun.createdAt).toLocaleDateString() }}</td>
            <td class="actions-column" @click.stop>
              <button 
                class="action-button edit"
                @click="emit('edit', testRun)"
              >
                ✏️
              </button>
            </td>
          </tr>
        </template>
      </tbody>
    </table>
  </div>
</template>

<style lang="scss" scoped>
.test-runs-table {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;

  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 12px 16px;
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  th {
    background-color: #f9fafb;
    font-weight: 500;
    color: #374151;
  }

  .checkbox-column {
    width: 40px;
    text-align: center;

    input[type="checkbox"] {
      cursor: pointer;
    }
  }

  .loading-cell, .empty-cell {
    text-align: center;
    padding: 40px;
    color: #6b7280;
  }

  .test-run-link {
    color: #e94560;
    text-decoration: none;
    font-weight: 500;

    &:hover {
      text-decoration: underline;
    }
  }

  .status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }

  .type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;

    &.general {
      background-color: #e0f2fe;
      color: #0277bd;
      border: 1px solid #b3e5fc;
    }

    &.security {
      background-color: #fff3e0;
      color: #f57c00;
      border: 1px solid #ffcc02;
    }
  }

  .actions-column {
    width: 200px;
  }

  .action-button {
    padding: 6px 12px;
    border-radius: 4px;
    font-size: 14px;
    cursor: pointer;
    border: 1px solid #e5e7eb;
    background-color: white;

    &.edit {
      color: #059669;
      
      &:hover {
        background-color: #f0fdf4;
      }
    }

    &.delete {
      color: #dc2626;
      
      &:hover {
        background-color: #fef2f2;
      }
    }
  }

  .test-run-row {
    font-size: 14px;
    cursor: pointer;

    &:hover {
      background-color: #f9fafb;
    }
  }
}
</style>
