<template>
  <div v-if="isOpen" class="modal-backdrop">
    <div class="modal-content">
      <div class="modal-header">
        <h3>{{ title }}</h3>
      </div>
      <div class="modal-body">
        <p>{{ message }}</p>

        <div v-if="isProcessing" class="progress-container">
          <div class="progress-bar" :style="{ width: `${progressValue}%` }"></div>
          <div class="progress-text">{{ progressMessage }}</div>
        </div>

        <div v-if="bulkDeleteInProgress" class="delete-progress">
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{width: `${(bulkDeleteProgress / bulkDeleteTotal) * 100}%`}"
            ></div>
          </div>
          <div class="progress-text">
            Deleting {{ bulkDeleteProgress }} of {{ bulkDeleteTotal }}
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <div v-if="!isProcessing" class="button-group">
          <button class="cancel-button" @click="$emit('cancel')">Cancel</button>
          <button class="confirm-button" @click="$emit('confirm')">Confirm</button>
        </div>
        <div v-else class="processing-indicator">
          Processing...
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  title: {
    type: String,
    default: 'Confirm'
  },
  message: {
    type: String,
    default: 'Are you sure?'
  },
  isProcessing: {
    type: Boolean,
    default: false
  },
  progressValue: {
    type: Number,
    default: 0
  },
  progressMessage: {
    type: String,
    default: 'Processing...'
  },
  bulkDeleteInProgress: {
    type: Boolean,
    default: false
  },
  bulkDeleteProgress: {
    type: Number,
    default: 0
  },
  bulkDeleteTotal: {
    type: Number,
    default: 0
  }
});

defineEmits(['confirm', 'cancel']);
</script>

<style scoped>
.modal-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background-color: white;
  border-radius: 8px;
  width: 400px;
  max-width: 90%;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-header {
  padding: 16px;
  border-bottom: 1px solid #eee;
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
}

.modal-body {
  padding: 16px;
}

.modal-footer {
  padding: 16px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
}

.button-group {
  display: flex;
  gap: 8px;
}

.cancel-button {
  background-color: #f2f2f2;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.confirm-button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}

.progress-container {
  margin-top: 16px;
  background-color: #f2f2f2;
  border-radius: 4px;
  height: 20px;
  overflow: hidden;
  position: relative;
}

.progress-bar {
  background-color: #4caf50;
  height: 100%;
  transition: width 0.3s ease;
}

.progress-text {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  text-align: center;
  line-height: 20px;
  font-size: 12px;
  color: #333;
}

.processing-indicator {
  font-style: italic;
  color: #666;
}
</style>