import { ref } from 'vue';
import axios from 'axios';

const API_URL = `${(import.meta as any).env.VITE_BACKEND_URL}/analytics`;

export function useReportData() {
  const loading = ref(false);
  const error = ref<string | null>(null);

  const handleRequest = async <T>(request: () => Promise<T>): Promise<T | null> => {
    try {
      loading.value = true;
      error.value = null;
      return await request();
    } catch (err: any) {
      console.error('Report data request failed:', err);
      error.value = err.response?.data?.message || 'Failed to fetch report data';
      return null;
    } finally {
      loading.value = false;
    }
  };

  // Project-specific analytics
  const getProjectTestCaseAnalytics = async (projectId: string) => {
    return handleRequest(async () => {
      const response = await axios.get(`${API_URL}/projects/${projectId}/test-cases`);
      return response.data;
    });
  };

  const getProjectTestRunPerformance = async (projectId: string) => {
    return handleRequest(async () => {
      const response = await axios.get(`${API_URL}/projects/${projectId}/test-runs`);
      return response.data;
    });
  };

  const getProjectExecutionEfficiency = async (projectId: string) => {
    return handleRequest(async () => {
      const response = await axios.get(`${API_URL}/projects/${projectId}/execution-efficiency`);
      return response.data;
    });
  };

  // Cross-project analytics
  const getCrossProjectQualityComparison = async () => {
    return handleRequest(async () => {
      const response = await axios.get(`${API_URL}/cross-project/quality-comparison`);
      return response.data;
    });
  };

  const getOrganizationTestingHealth = async () => {
    return handleRequest(async () => {
      const response = await axios.get(`${API_URL}/organization/testing-health`);
      return response.data;
    });
  };

  const getAITestingImpact = async () => {
    return handleRequest(async () => {
      const response = await axios.get(`${API_URL}/ai-testing/impact`);
      return response.data;
    });
  };

  // Mock data generators for development (when backend endpoints are not available)
  const getMockProjectTestCaseAnalytics = () => {
    return {
      automationCoverage: [
        { type: 'manual', count: '45' },
        { type: 'automation', count: '32' }
      ],
      typeDistribution: [
        { type: 'functional', count: '35' },
        { type: 'integration', count: '25' },
        { type: 'unit', count: '17' }
      ],
      priorityDistribution: [
        { priority: 'high', count: '15' },
        { priority: 'medium', count: '40' },
        { priority: 'low', count: '22' }
      ],
      platformDistribution: [
        { platform: 'web', count: '50' },
        { platform: 'mobile', count: '20' },
        { platform: 'api', count: '7' }
      ],
      growthOverTime: [
        { month: '2024-01-01', count: '5' },
        { month: '2024-02-01', count: '8' },
        { month: '2024-03-01', count: '12' },
        { month: '2024-04-01', count: '15' },
        { month: '2024-05-01', count: '18' },
        { month: '2024-06-01', count: '19' }
      ]
    };
  };

  const getMockProjectTestRunPerformance = () => {
    return {
      passFail: [
        { testRunName: 'Sprint 5 Regression', status: 'passed', count: '25' },
        { testRunName: 'Sprint 5 Regression', status: 'failed', count: '3' },
        { testRunName: 'Sprint 4 Regression', status: 'passed', count: '22' },
        { testRunName: 'Sprint 4 Regression', status: 'failed', count: '5' },
        { testRunName: 'Sprint 3 Regression', status: 'passed', count: '20' },
        { testRunName: 'Sprint 3 Regression', status: 'failed', count: '7' }
      ],
      executionTime: [
        { testRunName: 'Sprint 5 Regression', avgDuration: '45.5' },
        { testRunName: 'Sprint 4 Regression', avgDuration: '52.3' },
        { testRunName: 'Sprint 3 Regression', avgDuration: '48.7' }
      ],
      flakyTests: [
        { testCaseTitle: 'Login with valid credentials', statusCount: '2', totalRuns: '5' },
        { testCaseTitle: 'Add item to cart', statusCount: '2', totalRuns: '4' }
      ]
    };
  };

  const getMockCrossProjectQualityComparison = () => {
    return {
      projectCoverage: [
        { projectName: 'E-commerce App', totalTestCases: '77', automatedTestCases: '32' },
        { projectName: 'Mobile Banking', totalTestCases: '95', automatedTestCases: '68' },
        { projectName: 'CRM System', totalTestCases: '120', automatedTestCases: '45' }
      ],
      projectPassRates: [
        { projectName: 'E-commerce App', totalTests: '150', passedTests: '135' },
        { projectName: 'Mobile Banking', totalTests: '200', passedTests: '185' },
        { projectName: 'CRM System', totalTests: '180', passedTests: '160' }
      ]
    };
  };

  const getMockOrganizationTestingHealth = () => {
    return {
      overallAutomation: [
        { type: 'manual', count: '180' },
        { type: 'automation', count: '145' }
      ],
      testingVelocity: [
        { projectName: 'E-commerce App', month: '2024-04-01', testRunCount: '8' },
        { projectName: 'E-commerce App', month: '2024-05-01', testRunCount: '12' },
        { projectName: 'Mobile Banking', month: '2024-04-01', testRunCount: '15' },
        { projectName: 'Mobile Banking', month: '2024-05-01', testRunCount: '18' }
      ],
      qualityTrends: [
        { week: '2024-04-01', totalTests: '450', passedTests: '405' },
        { week: '2024-04-08', totalTests: '480', passedTests: '432' },
        { week: '2024-04-15', totalTests: '520', passedTests: '468' },
        { week: '2024-04-22', totalTests: '510', passedTests: '459' }
      ]
    };
  };

  const getMockAITestingImpact = () => {
    return {
      aiTestCases: [
        { isAIGenerated: true, count: '45' },
        { isAIGenerated: false, count: '280' }
      ],
      aiVsManualResults: [
        { isAIGenerated: true, status: 'passed', count: '38' },
        { isAIGenerated: true, status: 'failed', count: '7' },
        { isAIGenerated: false, status: 'passed', count: '245' },
        { isAIGenerated: false, status: 'failed', count: '35' }
      ]
    };
  };

  // Development mode: use mock data if backend is not available
  const getProjectTestCaseAnalyticsWithFallback = async (projectId: string) => {
    const result = await getProjectTestCaseAnalytics(projectId);
    return result || getMockProjectTestCaseAnalytics();
  };

  const getProjectTestRunPerformanceWithFallback = async (projectId: string) => {
    const result = await getProjectTestRunPerformance(projectId);
    return result || getMockProjectTestRunPerformance();
  };

  const getProjectExecutionEfficiencyWithFallback = async (projectId: string) => {
    const result = await getProjectExecutionEfficiency(projectId);
    return result || { automationStats: [], executionFrequency: [] };
  };

  const getCrossProjectQualityComparisonWithFallback = async () => {
    const result = await getCrossProjectQualityComparison();
    return result || getMockCrossProjectQualityComparison();
  };

  const getOrganizationTestingHealthWithFallback = async () => {
    const result = await getOrganizationTestingHealth();
    return result || getMockOrganizationTestingHealth();
  };

  const getAITestingImpactWithFallback = async () => {
    const result = await getAITestingImpact();
    return result || getMockAITestingImpact();
  };

  return {
    loading,
    error,
    // Original functions
    getProjectTestCaseAnalytics,
    getProjectTestRunPerformance,
    getProjectExecutionEfficiency,
    getCrossProjectQualityComparison,
    getOrganizationTestingHealth,
    getAITestingImpact,
    // Functions with fallback to mock data
    getProjectTestCaseAnalyticsWithFallback,
    getProjectTestRunPerformanceWithFallback,
    getProjectExecutionEfficiencyWithFallback,
    getCrossProjectQualityComparisonWithFallback,
    getOrganizationTestingHealthWithFallback,
    getAITestingImpactWithFallback
  };
}
