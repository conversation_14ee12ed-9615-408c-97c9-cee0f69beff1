<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '../stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const email = ref('')
const emailError = ref('')
const password = ref('')
const errorMessage = ref('')

const validateEmail = () => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  emailError.value = emailPattern.test(email.value) ? '' : 'Invalid email format'
}

const handleLogin = async () => {
  try {
    await authStore.login(email.value, password.value)
    router.push('/')
  } catch (error: any) {
    errorMessage.value = error.message || 'Login failed. Please try again.'
  }
}

// const loginWithGoogle = () => {
//   // Implement Google OAuth login
//   console.log('Login with Google')
// }

// const loginWithGitHub = () => {
//   // Implement GitHub OAuth login
//   console.log('Login with GitHub')
// }
</script>

<template>
  <div class="login-container">
    <div class="login-header">
      <h1 class="logo">AgentQ</h1>
    </div>
    
    <div class="login-card">
      <h2 class="login-title">Sign in to your account</h2>
      <!-- <p class="login-subtitle">Or <a href="#" class="create-account-link">create new account</a></p> -->
      
      <!-- <div class="social-login">
        <button class="social-button google" @click="loginWithGoogle">
          <span class="icon">G</span>
          Continue with Google
        </button>
        
        <button class="social-button github" @click="loginWithGitHub">
          <span class="icon">GH</span>
          Continue with GitHub
        </button>
      </div>
      
      <div class="divider">
        <span>Or continue with</span>
      </div> -->
      
      <form @submit.prevent="handleLogin" class="login-form">
        <div class="form-group">
          <label for="email">Email address</label>
          <input 
            type="email" 
            id="email" 
            v-model="email" 
            required
            placeholder="Enter your email"
            @input="validateEmail"
          />
          <p v-if="emailError" class="error-message">{{ emailError }}</p>
        </div>
        
        <div class="form-group">
          <label for="password">Password</label>
          <input 
            type="password" 
            id="password" 
            v-model="password" 
            required
            placeholder="Enter your password"
          />
        </div>
        
        <!-- <div class="forgot-password">
          <a href="#">Forgot your password?</a>
        </div> -->
        
        <div v-if="errorMessage" class="error-message">
          {{ errorMessage }}
        </div>
        
        <button type="submit" class="login-button">Sign in</button>
      </form>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px;
}

.login-header {
  margin-bottom: 30px;
  text-align: center;
}

.logo {
  font-size: 28px;
  font-weight: bold;
  color: #e94560;
}

.login-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  padding: 40px;
  width: 100%;
  max-width: 450px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 8px;
  text-align: center;
}

.login-subtitle {
  color: #6b7280;
  text-align: center;
  margin-bottom: 24px;
}

.create-account-link {
  color: #e94560;
  text-decoration: none;
  font-weight: 500;
  
  &:hover {
    text-decoration: underline;
  }
}

.social-login {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 24px;
}

.social-button {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12px;
  border-radius: 6px;
  border: 1px solid #e5e7eb;
  background-color: white;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #f9fafb;
  }
  
  .icon {
    margin-right: 12px;
    font-weight: bold;
  }
}

.divider {
  display: flex;
  align-items: center;
  margin: 24px 0;
  
  &::before,
  &::after {
    content: "";
    flex: 1;
    border-bottom: 1px solid #e5e7eb;
  }
  
  span {
    padding: 0 12px;
    color: #6b7280;
    font-size: 14px;
  }
}

.login-form {
  display: flex;
  flex-direction: column;
}

.form-group {
  margin-bottom: 16px;
  
  label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #374151;
  }
  
  input {
    width: 100%;
    padding: 12px;
    border: 1px solid #e5e7eb;
    border-radius: 6px;
    font-size: 16px;
    
    &:focus {
      outline: none;
      border-color: #e94560;
      box-shadow: 0 0 0 2px rgba(233, 69, 96, 0.1);
    }
  }
}

.forgot-password {
  text-align: right;
  margin-bottom: 24px;
  
  a {
    color: #e94560;
    text-decoration: none;
    font-size: 14px;
    
    &:hover {
      text-decoration: underline;
    }
  }
}

.error-message {
  background-color: #fee2e2;
  color: #b91c1c;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
  font-size: 14px;
}

.login-button {
  background-color: #e94560;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 12px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #d63553;
  }
}
</style>