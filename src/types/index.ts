// src/types/index.ts

export interface Project {
    id: string;
    name: string;
    description: string;
    createdAt: string;
    updatedAt: string;
    createdBy?: string;
  }
  
export interface Tag {
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  }
  
export interface TestCase {
    id: string;
    projectId: string;
    tcId: number;
    title: string;
    type: string;
    priority: string;
    platform: string;
    testCaseType: string;
    precondition: string;
    steps: string;
    expectation: string;
    folderId: string | null;
    tags: Tag[];
    createdAt: string;
    updatedAt: string;
    automationByAgentq?: boolean;
  }
  
export interface Folder {
    id: string;
    name: string;
    parentId?: string | null;
    children?: Folder[];
    createdAt: string;
    updatedAt: string;
  }

export interface Profile {
  company: {
    subscription: {
      name: string;
      tokenLimit: number;
      remainingTokens: number;
      isEnterprise: boolean;
      endDate: string;
    };
  };
}
