import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import { Config } from '../config';

export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private storage: Storage | null = null;
  private bucketName: string;
  private isCloudStorageEnabled: boolean;

  constructor(private configService: ConfigService) {
    this.bucketName = Config.GCP_BUCKET_NAME;
    this.isCloudStorageEnabled = process.env.ENABLE_CLOUD_STORAGE === 'true';

    // Only initialize Google Cloud Storage if enabled and credentials are available
    if (this.isCloudStorageEnabled && this.hasValidGCPCredentials()) {
      try {
        this.storage = new Storage({
          projectId: Config.GCP_PROJECT_ID,
          credentials: {
            client_email: Config.GCP_CLIENT_EMAIL,
            private_key: Config.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
          },
        });
        this.logger.log('Google Cloud Storage initialized successfully');
      } catch (error) {
        this.logger.warn('Failed to initialize Google Cloud Storage:', error);
        this.storage = null;
        this.isCloudStorageEnabled = false;
      }
    } else {
      this.logger.log('Google Cloud Storage disabled - file will be stored in database only');
    }
  }

  async uploadFile(testResultId: string, logs: string[], bucketPath: string = 'test-data'): Promise<string> {
    // If cloud storage is not enabled or not available, return a local storage indicator
    if (!this.isCloudStorageEnabled || !this.storage) {
      this.logger.log(`File stored locally in database for test result: ${testResultId} (${logs.length} entries)`);
      return `local://test-data/${testResultId}`;
    }

    try {
      const fileName = ``;
      const file = this.storage.bucket(this.bucketName).file(fileName);

      this.logger.log(`File uploaded successfully to GCS for test result: ${testResultId}`);
      return `gs://${this.bucketName}/${fileName}`;
    } catch (error) {
      this.logger.error(`Failed to upload file to GCS for test result ${testResultId}:`, error);
      // Fallback to local storage indicator
      this.logger.log(`Falling back to local storage for test result: ${testResultId}`);
      return `local://${bucketPath}/`;
    }
  }

}