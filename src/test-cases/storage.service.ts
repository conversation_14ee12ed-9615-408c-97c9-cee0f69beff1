import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage } from '@google-cloud/storage';
import { Config } from '../config';

@Injectable()
export class StorageService {
  private readonly logger = new Logger(StorageService.name);
  private storage: Storage | null = null;
  private bucketName: string;
  private isCloudStorageEnabled: boolean;

  constructor(private readonly configService: ConfigService) {
    this.bucketName = Config.GCP_BUCKET_NAME;
    this.isCloudStorageEnabled = process.env.ENABLE_CLOUD_STORAGE === 'true';

    // Only initialize Google Cloud Storage if enabled and credentials are available
    if (this.isCloudStorageEnabled && this.hasValidGCPCredentials()) {
      try {
        this.storage = new Storage({
          projectId: Config.GCP_PROJECT_ID,
          credentials: {
            client_email: Config.GCP_CLIENT_EMAIL,
            private_key: Config.GCP_PRIVATE_KEY?.replace(/\\n/g, '\n'),
          },
        });
        this.logger.log('Google Cloud Storage initialized successfully');
      } catch (error) {
        this.logger.warn('Failed to initialize Google Cloud Storage:', error);
        this.storage = null;
        this.isCloudStorageEnabled = false;
      }
    } else {
      this.logger.log('Google Cloud Storage disabled - file will be stored in database only');
    }
  }

  /**
   * Upload file to Google Cloud Storage in the agentq/test-data folder
   * @param fileName - The file path within the bucket
   * @param fileBuffer - The file buffer
   * @param contentType - The file content type
   * @returns Promise<string> - The GCS URL or local storage indicator
   */
  async uploadFile(fileName: string, fileBuffer: Buffer, contentType: string): Promise<string> {
    // If cloud storage is not enabled or not available, return a local storage indicator
    if (!this.isCloudStorageEnabled || !this.storage) {
      this.logger.log(`File stored locally in database: ${fileName}`);
      return `local://agentq/test-data/${fileName}`;
    }

    try {
      // Ensure the file path starts with agentq/test-data
      const fullFileName = fileName.startsWith('test-data/')
        ? fileName
        : `test-data/${fileName}`;

      const file = this.storage.bucket(this.bucketName).file(fullFileName);

      // Upload the file
      await file.save(fileBuffer, {
        metadata: {
          contentType: contentType,
        },
      });

      this.logger.log(`File uploaded successfully to GCS: ${fullFileName}`);
      return `gs://${this.bucketName}/${fullFileName}`;
    } catch (error) {
      this.logger.error(`Failed to upload file to GCS: ${fileName}:`, error);
      // Fallback to local storage indicator
      this.logger.log(`Falling back to local storage for file: ${fileName}`);
      return `local://agentq/test-data/${fileName}`;
    }
  }

  /**
   * Check if Google Cloud Storage credentials are valid
   */
  private hasValidGCPCredentials(): boolean {
    return !!(Config.GCP_PROJECT_ID && Config.GCP_CLIENT_EMAIL && Config.GCP_PRIVATE_KEY);
  }

}