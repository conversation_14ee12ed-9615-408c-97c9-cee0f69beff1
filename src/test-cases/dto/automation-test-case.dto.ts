import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsString, IsUUID, ValidateNested, IsNumber, IsOptional, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';

class AutomationStep {
  @ApiProperty({
    description: 'Step number',
    example: 1
  })
  @IsNumber()
  step: number;

  @ApiProperty({
    description: 'Step name/description',
    example: 'Navigate to login page'
  })
  @IsString()
  stepName: string;

  @ApiProperty({
    description: 'Target selector for the action',
    example: '//input[@id="username"]',
    required: false
  })
  @IsString()
  @IsOptional()
  target: string;

  @ApiProperty({
    description: 'Value for the action (URL, text to write, etc.)',
    example: 'https://example.com/login',
    required: false
  })
  @IsString()
  @IsOptional()
  value: string;

  @ApiProperty({
    description: 'Prompt for AI-assisted steps',
    example: 'Check if the login form is displayed correctly',
    required: false
  })
  @IsString()
  @IsOptional()
  prompt: string;

  @ApiProperty({
    description: 'Flag to show interaction dropdown',
    example: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  showInteractionDropdown: boolean;

  @ApiProperty({
    description: 'Flag to show assertion dropdown',
    example: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  showAssertionDropdown: boolean;

  @ApiProperty({
    description: 'Flag to show prompt field',
    example: false,
    required: false
  })
  @IsBoolean()
  @IsOptional()
  showPromptField: boolean;

  @ApiProperty({
    description: 'Action type (goto, click, write, assertText, etc.)',
    example: 'goto',
    required: false
  })
  @IsString()
  @IsOptional()
  action?: string;

  @ApiProperty({
    description: 'Multiple actions for this step in JSON format',
    example: '[{"action":"prompt","target":"","value":"user visit site"},{"action":"click","target":"#login","value":""}]',
    required: false
  })
  @IsString()
  @IsOptional()
  Actions?: string;
}

export class AutomationTestCaseDto {
  @ApiProperty({
    description: 'The ID of the test case this automation belongs to',
    example: '123e4567-e89b-12d3-a456-************'
  })
  @IsUUID()
  @IsNotEmpty()
  testCaseId: string;

  @ApiProperty({
    description: 'The automation steps',
    type: [AutomationStep],
    example: [
      {
        step: 1,
        stepName: 'Navigate to login page',
        action: 'goto',
        value: 'https://example.com/login',
        target: '',
        prompt: '',
        showInteractionDropdown: false,
        showAssertionDropdown: false,
        showPromptField: false
      }
    ]
  })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => AutomationStep)
  steps: AutomationStep[];
}
