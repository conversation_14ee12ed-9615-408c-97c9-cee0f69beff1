// Test script to simulate User A and User B running tests simultaneously
const WebSocket = require('ws');

// Simulate User A
function simulateUserA() {
  console.log('👤 User A: Connecting to WebSocket...');
  const wsA = new WebSocket('ws://localhost:3021');
  
  wsA.on('open', () => {
    console.log('👤 User A: Connected, authenticating...');
    
    // Authenticate
    wsA.send(JSON.stringify({
      type: 'auth',
      token: 'test-api-key-user-a'
    }));
    
    // Wait a moment then send test
    setTimeout(() => {
      console.log('👤 User A: Sending test execution request...');
      wsA.send(JSON.stringify({
        type: 'execute_test',
        token: 'test-api-key-user-a',
        testCaseId: 'test-case-a',
        tcId: 'tc-a',
        steps: [
          { step: 1, stepName: 'Test Step A', action: 'click', target: '#button' }
        ],
        testCase: {
          title: 'User A Test Case',
          precondition: 'Test precondition',
          expectation: 'Test expectation'
        }
      }));
    }, 1000);
  });
  
  wsA.on('message', (data) => {
    const message = JSON.parse(data.toString());
    console.log('👤 User A received:', message.type, message.message || '');
  });
  
  wsA.on('error', (error) => {
    console.log('👤 User A error:', error.message);
  });
  
  return wsA;
}

// Simulate User B (starts 2 seconds after User A)
function simulateUserB() {
  console.log('👥 User B: Connecting to WebSocket...');
  const wsB = new WebSocket('ws://localhost:3021');
  
  wsB.on('open', () => {
    console.log('👥 User B: Connected, authenticating...');
    
    // Authenticate
    wsB.send(JSON.stringify({
      type: 'auth',
      token: 'test-api-key-user-b'
    }));
    
    // Wait a moment then send test
    setTimeout(() => {
      console.log('👥 User B: Sending test execution request...');
      wsB.send(JSON.stringify({
        type: 'execute_test',
        token: 'test-api-key-user-b',
        testCaseId: 'test-case-b',
        tcId: 'tc-b',
        steps: [
          { step: 1, stepName: 'Test Step B', action: 'type', target: '#input', value: 'test' }
        ],
        testCase: {
          title: 'User B Test Case',
          precondition: 'Test precondition',
          expectation: 'Test expectation'
        }
      }));
    }, 1000);
  });
  
  wsB.on('message', (data) => {
    const message = JSON.parse(data.toString());
    console.log('👥 User B received:', message.type, message.message || '');
  });
  
  wsB.on('error', (error) => {
    console.log('👥 User B error:', error.message);
  });
  
  return wsB;
}

// Run the simulation
console.log('🧪 Starting queue simulation test...');
console.log('📋 This will test if User B gets queued when User A is running a test\n');

// Start User A
const userA = simulateUserA();

// Start User B after 2 seconds
setTimeout(() => {
  const userB = simulateUserB();
  
  // Clean up after 30 seconds
  setTimeout(() => {
    console.log('\n🏁 Test completed, closing connections...');
    userA.close();
    userB.close();
    process.exit(0);
  }, 30000);
}, 2000);
