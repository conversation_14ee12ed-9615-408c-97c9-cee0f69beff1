server {
    listen 80;
    server_name _;

    root /usr/share/nginx/html;
    index index.html;

    # Redirect /signin to /login
    location = /signin {
        return 301 /login;
    }

    location = /agentq {
        return 301 /projects;
    }

    # Serve static assets locally
    location /assets/ {
        gzip_static on;
        expires max;
        add_header Cache-Control public;
    }

    # Handle Vue routes
    location ~ ^/(dashboard|login|system-health|projects|auth/external|reports) {
        try_files $uri $uri/ /index.html;
    }

    # Handle root path - serve the Vue app directly
    location = / {
        try_files $uri /index.html;
    }

    # Fallback for any other routes
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Optional: CORS headers
    add_header Access-Control-Allow-Origin *;
}