# Google Cloud Storage Setup for Video and Screenshot Storage

## Overview

The application supports storing test execution videos and screenshots in Google Cloud Storage (GCS). When GCS is not configured, the system will fall back to local storage indicators, but the actual media files won't be accessible through the UI.

## Current Status

Currently, the application is configured with placeholder GCS credentials, which means:
- ✅ Videos and screenshots are generated during test execution
- ✅ Upload attempts are made to the backend
- ❌ Files are not actually uploaded to GCS (fallback to local storage indicators)
- ❌ Videos and screenshots cannot be viewed in the UI

## Setting Up Google Cloud Storage

### 1. Create a Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Note your project ID

### 2. Create a Storage Bucket

1. Navigate to Cloud Storage in the Google Cloud Console
2. Create a new bucket (e.g., `agentq-test-artifacts`)
3. Choose appropriate settings for your use case
4. Note the bucket name

### 3. Create a Service Account

1. Go to IAM & Admin > Service Accounts
2. Create a new service account
3. Grant the following roles:
   - Storage Object Admin (for the specific bucket)
   - Storage Legacy Bucket Reader (if needed)

### 4. Generate Service Account Key

1. Click on the created service account
2. Go to the "Keys" tab
3. Click "Add Key" > "Create new key"
4. Choose JSON format
5. Download the key file

### 5. Configure Environment Variables

Update your `.env` file with the actual GCS credentials:

```env
# Google Cloud Storage Configuration
GCP_PROJECT_ID=your-actual-project-id
GCP_CLIENT_EMAIL=<EMAIL>
GCP_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nYOUR_ACTUAL_PRIVATE_KEY_HERE\n-----END PRIVATE KEY-----"
GCP_BUCKET_NAME=your-bucket-name

# Enable cloud storage
ENABLE_CLOUD_STORAGE=true
```

**Important Notes:**
- Replace all placeholder values with actual GCS credentials
- The private key should include the full key with proper line breaks (`\n`)
- Make sure the service account has proper permissions for the bucket

### 6. Restart the Application

After updating the environment variables:
1. Restart the backend server
2. Restart the WebSocket test runner server (port 3022)

## Verification

To verify that GCS is working correctly:

1. Run a test from the test run detail page
2. Check the backend logs for successful upload messages:
   ```
   Video uploaded successfully for test result: [ID]
   Screenshot uploaded successfully for test result: [ID]
   ```
3. Check your GCS bucket for the uploaded files
4. Videos and screenshots should now be viewable in the UI

## Troubleshooting

### Common Issues

1. **"Screenshot/Video stored locally" messages**
   - This indicates GCS is not properly configured
   - Check that all environment variables are set correctly
   - Verify service account permissions

2. **Upload errors in logs**
   - Check service account permissions
   - Verify bucket exists and is accessible
   - Ensure private key format is correct

3. **Files not appearing in UI**
   - Check that files were actually uploaded to GCS
   - Verify signed URL generation is working
   - Check browser console for errors

### Debug Steps

1. Check backend logs during test execution
2. Verify GCS bucket contents in Google Cloud Console
3. Test bucket access with the service account credentials
4. Check network connectivity to Google Cloud Storage

## Security Considerations

- Keep service account keys secure
- Use least-privilege access (only necessary bucket permissions)
- Consider using Workload Identity if running on Google Cloud
- Regularly rotate service account keys
- Don't commit actual credentials to version control

## Cost Considerations

- GCS storage costs apply for stored videos and screenshots
- Consider implementing cleanup policies for old test artifacts
- Monitor storage usage and costs in Google Cloud Console
