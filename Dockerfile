# Build stage
FROM node:22-alpine AS builder
WORKDIR /app

# Declare the build argument
ARG VITE_CORE_SERVICE_URL
ARG VITE_BACKEND_URL
ARG VITE_AI_SERVICE_URL
ARG VITE_WEBSOCKET_URL
ARG VITE_WEBSOCKET_TESTRUN_URL
ARG VITE_WEBSOCKET_SECURITY_URL
ARG VITE_WEBSOCKET_TESTRUN_SECURITY_URL

# Set it as an environment variable so npm run build can pick it up
ENV VITE_CORE_SERVICE_URL=${VITE_CORE_SERVICE_URL}

# App backend for CRUD operations
ENV VITE_BACKEND_URL=${VITE_BACKEND_URL}

ENV VITE_AI_SERVICE_URL=${VITE_AI_SERVICE_URL}

# WebSocket Service for TestAutomation page
ENV VITE_WEBSOCKET_URL=${VITE_WEBSOCKET_URL}

# WebSocket Service for TestRun automation (separate server)
ENV VITE_WEBSOCKET_TESTRUN_URL=${VITE_WEBSOCKET_TESTRUN_URL}

# WebSocket Service for TestAutomationSecurity page
ENV VITE_WEBSOCKET_SECURITY_URL=${VITE_WEBSOCKET_SECURITY_URL}

# WebSocket Service for TestRun Security - DAST automation (separate server)
ENV VITE_WEBSOCKET_TESTRUN_SECURITY_URL=${VITE_WEBSOCKET_TESTRUN_SECURITY_URL}

COPY package*.json ./
COPY . .
RUN npm ci
RUN npm run build

# Production stage
FROM nginx:alpine
COPY --from=builder /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
