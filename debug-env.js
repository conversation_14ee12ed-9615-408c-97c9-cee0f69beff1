const { config } = require('dotenv');

// Load environment variables
config();

console.log('=== Environment Variables Debug ===');
console.log('REDIS_HOST:', process.env.REDIS_HOST);
console.log('REDIS_PORT:', process.env.REDIS_PORT);
console.log('REDIS_PASSWORD:', process.env.REDIS_PASSWORD ? '***' : 'undefined');
console.log('REDIS_DB:', process.env.REDIS_DB);
console.log('NODE_ENV:', process.env.NODE_ENV);
console.log('PWD:', process.cwd());

// Test Redis config object
const redisConfig = {
  host: process.env.REDIS_HOST || 'localhost',
  port: parseInt(process.env.REDIS_PORT || '6379'),
  password: process.env.REDIS_PASSWORD,
  maxRetriesPerRequest: null,
  retryDelayOnFailover: 100,
  enableReadyCheck: false,
  lazyConnect: true,
  connectTimeout: 10000,
  commandTimeout: 5000,
  db: parseInt(process.env.REDIS_DB || '0'),
};

console.log('\n=== Redis Config Object ===');
console.log(JSON.stringify({
  ...redisConfig,
  password: redisConfig.password ? '***' : 'undefined'
}, null, 2));
