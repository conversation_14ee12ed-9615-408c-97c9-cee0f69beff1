#!/usr/bin/env node

/**
 * Debug script to test Google Cloud Storage upload functionality
 * This script helps diagnose why videos and screenshots are not being uploaded to GCS
 */

const fs = require('fs');
const path = require('path');
const axios = require('axios');

// Configuration
const BACKEND_URL = process.env.VITE_BACKEND_URL || 'http://localhost:3010';
const TEST_RESULTS_DIR = path.join(process.cwd(), 'test-results');

console.log('🔍 GCS Upload Diagnostic Tool');
console.log('==============================');

async function checkEnvironmentVariables() {
  console.log('\n1. Checking Environment Variables...');
  
  const requiredVars = [
    'GCP_PROJECT_ID',
    'GCP_CLIENT_EMAIL', 
    'GCP_PRIVATE_KEY',
    'GCP_BUCKET_NAME'
  ];
  
  const envFile = path.join(process.cwd(), '.env');
  if (!fs.existsSync(envFile)) {
    console.log('❌ .env file not found');
    return false;
  }
  
  const envContent = fs.readFileSync(envFile, 'utf8');
  let allValid = true;
  
  requiredVars.forEach(varName => {
    const match = envContent.match(new RegExp(`${varName}=(.+)`));
    if (!match) {
      console.log(`❌ ${varName} not found in .env`);
      allValid = false;
    } else {
      const value = match[1].trim();
      if (value.includes('your_') || value.includes('YOUR_') || value === '') {
        console.log(`❌ ${varName} has placeholder value: ${value.substring(0, 30)}...`);
        allValid = false;
      } else {
        console.log(`✅ ${varName} is configured`);
      }
    }
  });
  
  return allValid;
}

async function checkTestResultsDirectory() {
  console.log('\n2. Checking Test Results Directory...');
  
  if (!fs.existsSync(TEST_RESULTS_DIR)) {
    console.log(`❌ Test results directory not found: ${TEST_RESULTS_DIR}`);
    return false;
  }
  
  console.log(`✅ Test results directory exists: ${TEST_RESULTS_DIR}`);
  
  // Find video and screenshot files
  const findFiles = (dir, extension) => {
    const files = [];
    try {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory()) {
          files.push(...findFiles(fullPath, extension));
        } else if (item.endsWith(extension)) {
          files.push(fullPath);
        }
      }
    } catch (error) {
      // Ignore errors
    }
    return files;
  };
  
  const videoFiles = findFiles(TEST_RESULTS_DIR, '.webm');
  const screenshotFiles = findFiles(TEST_RESULTS_DIR, '.png');
  
  console.log(`📹 Found ${videoFiles.length} video files`);
  console.log(`📸 Found ${screenshotFiles.length} screenshot files`);
  
  if (videoFiles.length > 0) {
    console.log('Recent video files:');
    videoFiles.slice(-3).forEach(file => {
      const stat = fs.statSync(file);
      console.log(`  - ${file} (${(stat.size / 1024 / 1024).toFixed(2)} MB, ${stat.mtime.toISOString()})`);
    });
  }
  
  if (screenshotFiles.length > 0) {
    console.log('Recent screenshot files:');
    screenshotFiles.slice(-3).forEach(file => {
      const stat = fs.statSync(file);
      console.log(`  - ${file} (${(stat.size / 1024).toFixed(2)} KB, ${stat.mtime.toISOString()})`);
    });
  }
  
  return videoFiles.length > 0 || screenshotFiles.length > 0;
}

async function checkBackendConnection() {
  console.log('\n3. Checking Backend Connection...');
  
  try {
    const response = await axios.get(`${BACKEND_URL}/health`, { timeout: 5000 });
    console.log(`✅ Backend is accessible at ${BACKEND_URL}`);
    return true;
  } catch (error) {
    console.log(`❌ Backend not accessible at ${BACKEND_URL}`);
    console.log(`   Error: ${error.message}`);
    return false;
  }
}

async function checkGCSConfiguration() {
  console.log('\n4. Checking Backend GCS Configuration...');
  
  try {
    // Try to access a test endpoint that would reveal GCS status
    const response = await axios.get(`${BACKEND_URL}/projects`, { 
      timeout: 5000,
      headers: {
        'Authorization': 'Bearer test-token' // This will fail but might give us info
      }
    });
  } catch (error) {
    if (error.response?.status === 401) {
      console.log('✅ Backend is running (authentication required)');
    } else {
      console.log(`⚠️  Backend response: ${error.response?.status || 'No response'}`);
    }
  }
  
  // Check if we can determine GCS status from logs or other means
  console.log('💡 To check GCS configuration on backend:');
  console.log('   1. Check backend logs for "Google Cloud Storage initialized" messages');
  console.log('   2. Look for "GCS not configured" or credential errors');
  console.log('   3. Verify the backend .env file has real GCS credentials');
}

async function simulateUpload() {
  console.log('\n5. Simulating Upload Process...');
  
  // Find the most recent video file
  const findFiles = (dir, extension) => {
    const files = [];
    try {
      const items = fs.readdirSync(dir);
      for (const item of items) {
        const fullPath = path.join(dir, item);
        const stat = fs.statSync(fullPath);
        if (stat.isDirectory()) {
          files.push(...findFiles(fullPath, extension));
        } else if (item.endsWith(extension)) {
          files.push({ path: fullPath, mtime: stat.mtime });
        }
      }
    } catch (error) {
      // Ignore errors
    }
    return files;
  };
  
  const videoFiles = findFiles(TEST_RESULTS_DIR, '.webm');
  
  if (videoFiles.length === 0) {
    console.log('❌ No video files found to test upload');
    return;
  }
  
  // Sort by modification time and get the most recent
  const mostRecentVideo = videoFiles.sort((a, b) => b.mtime - a.mtime)[0];
  console.log(`📹 Testing with most recent video: ${mostRecentVideo.path}`);
  
  // Extract project info from path if possible
  const pathParts = mostRecentVideo.path.split(path.sep);
  const testResultsIndex = pathParts.findIndex(part => part === 'test-results');
  
  if (testResultsIndex >= 0 && pathParts.length > testResultsIndex + 3) {
    const projectId = pathParts[testResultsIndex + 1];
    const testRunId = pathParts[testResultsIndex + 2];
    const tcId = pathParts[testResultsIndex + 3];
    
    console.log(`📊 Extracted info: Project=${projectId}, TestRun=${testRunId}, TC=${tcId}`);
    
    // Try to get test results for this project/test run
    try {
      const response = await axios.get(
        `${BACKEND_URL}/projects/${projectId}/test-runs/${testRunId}/test-results?page=1&limit=10`,
        { timeout: 5000 }
      );
      console.log(`✅ Found ${response.data.results?.length || 0} test results`);
    } catch (error) {
      console.log(`❌ Could not fetch test results: ${error.response?.status || error.message}`);
    }
  }
}

async function main() {
  try {
    const envValid = await checkEnvironmentVariables();
    const filesExist = await checkTestResultsDirectory();
    const backendOk = await checkBackendConnection();
    
    await checkGCSConfiguration();
    await simulateUpload();
    
    console.log('\n📋 Summary:');
    console.log('===========');
    
    if (!envValid) {
      console.log('❌ ISSUE: GCS environment variables have placeholder values');
      console.log('   SOLUTION: Update .env file with real GCS credentials');
      console.log('   See GOOGLE_CLOUD_STORAGE_SETUP.md for instructions');
    }
    
    if (!filesExist) {
      console.log('❌ ISSUE: No test result files found');
      console.log('   SOLUTION: Run a test to generate video/screenshot files');
    }
    
    if (!backendOk) {
      console.log('❌ ISSUE: Backend not accessible');
      console.log('   SOLUTION: Start the backend server on port 3010');
    }
    
    if (envValid && filesExist && backendOk) {
      console.log('✅ All basic checks passed');
      console.log('💡 If uploads still fail, check backend logs for GCS errors');
    }
    
  } catch (error) {
    console.error('❌ Diagnostic failed:', error.message);
  }
}

// Run the diagnostic
main();
